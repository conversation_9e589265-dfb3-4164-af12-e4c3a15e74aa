# ZapReiDelas - WhatsApp Bot

Um bot automatizado para WhatsApp que processa dados de clientes e gerencia interações através de mensagens personalizadas.

## 📋 Funcionalidades

- **Processamento de CSV**: Lê dados de clientes de arquivo CSV
- **Envio Automatizado**: Envia mensagens personalizadas via WhatsApp
- **Gerenciamento de Tokens**: Sistema de validação com tokens de 6 dígitos
- **Templates de Mensagens**: Sistema de templates para mensagens padronizadas
- **Banco de Dados**: Armazena interações e status dos clientes
- **Filtros Avançados**: Script Python para filtrar dados por gênero e renda
- **Validação WhatsApp**: Verifica se números estão registrados no WhatsApp

## 🛠️ Tecnologias Utilizadas

- **Node.js** - Runtime JavaScript
- **whatsapp-web.js** - Biblioteca para integração com WhatsApp Web
- **SQLite** - Banco de dados local
- **Puppeteer** - Controle do navegador
- **Python** - Scripts de processamento de dados
- **CSV Parser** - Processamento de arquivos CSV

## 📁 Estrutura do Projeto

```
├── server-win.js              # Arquivo principal do bot
├── filter_by_gender_income.py # Script de filtro de dados
├── package.json               # Dependências do Node.js
├── assets/                    # Imagens para envio
│   ├── img1.jpeg
│   └── img2.jpeg
├── templates/                 # Templates de mensagens
│   ├── mensagem_inicial.txt
│   ├── confirmacao.txt
│   ├── cancelamento_confirmado.txt
│   └── ...
└── dados.csv                 # Arquivo de dados (não versionado)
```

## 🚀 Instalação

1. **Clone o repositório**
```bash
git clone <url-do-repositorio>
cd zapreidelas
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure os arquivos necessários**
   - Adicione o arquivo `dados.csv` com os dados dos clientes
   - Configure as imagens na pasta `assets/`
   - Ajuste os templates na pasta `templates/`

## 📊 Uso

### Executar o Bot Principal
```bash
npm start
```

### Executar Filtro de Dados
```bash
python3 filter_by_gender_income.py
```

## ⚙️ Configuração

### Estrutura do CSV
O arquivo `dados.csv` deve conter as seguintes colunas:
- `NOME`: Nome do cliente
- `TELEFONE`: Número de telefone
- `CREDENCIAL`: CPF do cliente
- `EMAIL`: Email do cliente
- `RENDA`: Renda do cliente

### Templates de Mensagens
Os templates estão localizados na pasta `templates/` e suportam variáveis:
- `{{NOME}}`: Nome do cliente
- `{{CPF}}`: CPF formatado
- `{{RENDA}}`: Valor da renda
- `{{TELEFONE_OFUSCADO}}`: Telefone parcialmente oculto

## 🔒 Segurança

- Dados sensíveis não são versionados (`.gitignore`)
- Tokens de validação com expiração
- Validação de entrada de dados
- Logs de auditoria das interações

## 📝 Licença

Este projeto é privado e confidencial.

## 🤝 Contribuição

Este é um projeto privado. Para contribuições, entre em contato com o administrador.

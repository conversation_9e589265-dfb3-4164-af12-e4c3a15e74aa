{"ADDRESS_HOME_STREET_NAME": {"en": [{"pattern_identifier": "en_street_name", "positive_pattern": "street", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}], "de": [{"pattern_identifier": "de_street_name", "positive_pattern": "stra(ss|ß)e", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}], "es": [{"pattern_identifier": "es_street_name", "positive_pattern": "calle", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}], "ru": [{"pattern_identifier": "ru_street_name", "positive_pattern": "улица|название.?улицы", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}], "pt": [{"pattern_identifier": "pt_street_name", "positive_pattern": "rua|avenida|((?<!do |de )endereço)", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}]}, "ADDRESS_HOME_APT_NUM": {"en": [{"pattern_identifier": "en_apartment_number", "positive_pattern": "apartment", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "es": [{"pattern_identifier": "es_apartment_number", "positive_pattern": "interior|número.*apartamento", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "de": [{"pattern_identifier": "de_apartment_number", "positive_pattern": "wohnung", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ru": [{"pattern_identifier": "ru_apartment_number", "positive_pattern": "к<PERSON>а<PERSON><PERSON><PERSON><PERSON>", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "it": [{"pattern_identifier": "it_apartment_number", "positive_pattern": "numero.*appartamento", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "fr": [{"pattern_identifier": "fr_apartment_number", "positive_pattern": "numéro.*appartement", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "ADDRESS_HOME_HOUSE_NUMBER": {"en": [{"pattern_identifier": "en_house_number", "positive_pattern": "(house.?|street.?|^)number", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "de": [{"pattern_identifier": "de_house_number", "positive_pattern": "(haus|^)(nummer|nr\\.?)", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "pt": [{"pattern_identifier": "pt_house_number", "positive_pattern": "^\\*?.?número(.?\\*?$| da residência)", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "es": [{"pattern_identifier": "es_house_number", "positive_pattern": "n(u|ú)mero.*apartamento|exterior", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ru": [{"pattern_identifier": "ru_house_number", "positive_pattern": "дом|номер.?дома", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "ATTENTION_IGNORED": {"en": [{"pattern_identifier": "en_attention_ignored_preserving", "positive_pattern": "attention|attn", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "REGION_IGNORED": {"en": [{"pattern_identifier": "en_region_ignored_preserving", "positive_pattern": "province|region|other", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_region_ignored_preserving", "positive_pattern": "provincia", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "pt": [{"pattern_identifier": "pt_region_ignored_preserving", "positive_pattern": "bairro|suburb", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "ADDRESS_NAME_IGNORED": {"en": [{"pattern_identifier": "en_address_name_ignored_preserving", "positive_pattern": "address.*nickname|address.*label", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "tr": [{"pattern_identifier": "tr_address_name_ignored_preserving", "positive_pattern": "adres ([İi]sim|başlığı|adı)", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "pt": [{"pattern_identifier": "pt_address_name_ignored_preserving", "positive_pattern": "identificação do endereço", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "id": [{"pattern_identifier": "id_address_name_ignored_preserving", "positive_pattern": "(label|judul|nama) alamat", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "COMPANY_NAME": {"en": [{"pattern_identifier": "en_company_preserving", "positive_pattern": "company|business|organization|organisation", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "de": [{"pattern_identifier": "de_company_preserving", "positive_pattern": "(?<!con)firma|firmenname", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_company_preserving", "positive_pattern": "empresa", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fr": [{"pattern_identifier": "fr_company_preserving", "positive_pattern": "societe|société", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "it": [{"pattern_identifier": "it_company_preserving", "positive_pattern": "ragione.?sociale", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ja": [{"pattern_identifier": "ja_company_preserving", "positive_pattern": "会社", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ru": [{"pattern_identifier": "ru_company_preserving", "positive_pattern": "название.?компании", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "zh-CN": [{"pattern_identifier": "zh_company_preserving", "positive_pattern": "单位|公司", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fa": [{"pattern_identifier": "fa_company_preserving", "positive_pattern": "شرکت", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ko": [{"pattern_identifier": "ko_company_preserving", "positive_pattern": "회사|직장", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "id": [{"pattern_identifier": "id_company_preserving", "positive_pattern": "(nama.?)?per<PERSON><PERSON>an", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "ADDRESS_LINE_1": {"en": [{"pattern_identifier": "en_address_line_1_preserving", "positive_pattern": "^address$|address[_-]?line(one)?|address1|addr1|street|(?:shipping|billing)address$|house.?name", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}, {"pattern_identifier": "en_address_line_1_label_preserving", "positive_pattern": "(^\\W*address)|(address\\W*$)|(?:shipping|billing|mailing|pick.?up|drop.?off|delivery|sender|postal|recipient|home|work|office|school|business|mail)[\\s\\-]+address|address\\s+(of|for|to|from)|street.*(house|building|apartment|floor)|(house|building|apartment|floor).*street", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 129}], "de": [{"pattern_identifier": "de_address_line_1_preserving", "positive_pattern": "strasse|straße", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}], "es": [{"pattern_identifier": "es_address_line_1_preserving", "positive_pattern": "direccion|dirección", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}], "fr": [{"pattern_identifier": "fr_address_line_1_preserving", "positive_pattern": "adresse", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}, {"pattern_identifier": "fr_address_line_1_label_preserving", "positive_pattern": "adresse", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 129}], "it": [{"pattern_identifier": "it_address_line_1_preserving", "positive_pattern": "<PERSON><PERSON><PERSON><PERSON>", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}, {"pattern_identifier": "it_address_line_1_label_preserving", "positive_pattern": "<PERSON><PERSON><PERSON><PERSON>", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 129}], "ja": [{"pattern_identifier": "ja_address_line_1_preserving", "positive_pattern": "^住所$|住所1", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}, {"pattern_identifier": "ja_address_line_1_label_preserving", "positive_pattern": "住所", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 129}], "pt": [{"pattern_identifier": "pt_address_line_1_preserving", "positive_pattern": "morada|((?<!do |de )endereço)", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}], "ru": [{"pattern_identifier": "ru_address_line_1_preserving", "positive_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}, {"pattern_identifier": "ru_address_line_1_label_preserving", "positive_pattern": "улиц.*(дом|корпус|квартир|этаж)|(дом|корпус|квартир|этаж).*улиц", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 129}], "zh-CN": [{"pattern_identifier": "zh_address_line_1_preserving", "positive_pattern": "地址", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}], "tr": [{"pattern_identifier": "tr_address_line_1_preserving", "positive_pattern": "(\\b|_)adres(?! tarifi)(\\b|_)", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}, {"pattern_identifier": "tr_address_line_1_label_preserving", "positive_pattern": "(\\b|_)adres(?! tarifi)(\\b|_)|(sokak|cadde).*(apartman|bina|daire|mahalle)|(apartman|bina|daire|mahalle).*(sokak|cadde)", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 129}], "ko": [{"pattern_identifier": "ko_address_line_1_preserving", "positive_pattern": "^주소.?$|주소.?1", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}, {"pattern_identifier": "ko_address_line_1_label_preserving", "positive_pattern": "주소", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 129}], "id": [{"pattern_identifier": "id_address_line_1_preserving", "positive_pattern": "^alamat", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 129}, {"pattern_identifier": "id_address_line_1_label_preserving", "positive_pattern": "^alamat", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 129}]}, "ADDRESS_LINE_2": {"en": [{"pattern_identifier": "en_address_line_2_preserving", "positive_pattern": "address[_-]?line(2|two)|address2|addr2|street|suite|unit", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}, {"pattern_identifier": "en_address_line_2_label_preserving", "positive_pattern": "address|line", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 1}], "de": [{"pattern_identifier": "de_address_line_2_preserving", "positive_pattern": "adresszusatz|ergänzende.?angaben", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_address_line_2_preserving", "positive_pattern": "direccion2|colonia|adicional", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fr": [{"pattern_identifier": "fr_address_line_2_preserving", "positive_pattern": "addresssuppl|complementnom|appartement", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}, {"pattern_identifier": "fr_address_line_2_label_preserving", "positive_pattern": "adresse", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 1}], "it": [{"pattern_identifier": "it_address_line_2_preserving", "positive_pattern": "indirizzo2", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}, {"pattern_identifier": "it_address_line_2_label_preserving", "positive_pattern": "<PERSON><PERSON><PERSON><PERSON>", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 1}], "ja": [{"pattern_identifier": "ja_address_line_2_preserving", "positive_pattern": "住所2", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "pt": [{"pattern_identifier": "pt_address_line_2_preserving", "positive_pattern": "complemento|addrcomplement", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ru": [{"pattern_identifier": "ru_address_line_2_preserving", "positive_pattern": "Улица", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "zh-CN": [{"pattern_identifier": "zh_address_line_2_preserving", "positive_pattern": "地址2", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}, {"pattern_identifier": "zh_address_line_2_label_preserving", "positive_pattern": "地址", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 1}], "ko": [{"pattern_identifier": "ko_address_line_2_preserving", "positive_pattern": "주소.?2", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}, {"pattern_identifier": "ko_address_line_2_label_preserving", "positive_pattern": "주소", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 1, "match_field_input_types": 1}]}, "ADDRESS_LINE_EXTRA": {"en": [{"pattern_identifier": "en_address_line_extra_preserving", "positive_pattern": "address.*line[3-9]|address[3-9]|addr[3-9]|street|line[3-9]", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_address_line_extra_preserving", "positive_pattern": "municipio", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fr": [{"pattern_identifier": "fr_address_line_extra_preserving", "positive_pattern": "batiment|residence", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "it": [{"pattern_identifier": "it_address_line_extra_preserving", "positive_pattern": "<PERSON><PERSON><PERSON><PERSON>[3-9]", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "ADDRESS_LOOKUP": {"en": [{"pattern_identifier": "en_address_lookup_preserving", "positive_pattern": "lookup", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "COUNTRY": {"en": [{"pattern_identifier": "en_country_preserving", "positive_pattern": "country|countries", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "es": [{"pattern_identifier": "es_country_preserving", "positive_pattern": "país|pais", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "de": [{"pattern_identifier": "de_country_preserving", "positive_pattern": "(\\b|_)land(\\b|_)(?!.*(mark.*))", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ja": [{"pattern_identifier": "ja_country_preserving", "positive_pattern": "(?<!(入|出))国", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "zh-CN": [{"pattern_identifier": "zh_country_preserving", "positive_pattern": "国家", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ko": [{"pattern_identifier": "ko_country_preserving", "positive_pattern": "국가|나라", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "tr": [{"pattern_identifier": "tr_country_preserving", "positive_pattern": "(\\b|_)(ülke|ulce|ulke)(\\b|_)", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "fa": [{"pattern_identifier": "fa_country_preserving", "positive_pattern": "کشور", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "id": [{"pattern_identifier": "id_country_preserving", "positive_pattern": "negara", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}]}, "COUNTRY_LOCATION": {"en": [{"pattern_identifier": "en_country_location_preserving", "positive_pattern": "location", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 136}]}, "ZIP_CODE": {"en": [{"pattern_identifier": "en_zip_code_preserving", "positive_pattern": "zip|postal|post.*code|pcode|pin.?code", "positive_score": 1.1, "negative_pattern": "\\.zip", "negative_patterns_explainer": ".zip refers to a file extension", "match_field_attributes": 3, "match_field_input_types": 69}], "de": [{"pattern_identifier": "de_zip_code_preserving", "positive_pattern": "<PERSON><PERSON><PERSON><PERSON>", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "es": [{"pattern_identifier": "es_zip_code_preserving", "positive_pattern": "\\bcp\\b", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "fr": [{"pattern_identifier": "fr_zip_code_preserving", "positive_pattern": "\\bcdp\\b", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "it": [{"pattern_identifier": "it_zip_code_preserving", "positive_pattern": "\\bcap\\b", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ja": [{"pattern_identifier": "ja_zip_code_preserving", "positive_pattern": "郵便番号", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "pt": [{"pattern_identifier": "pt_zip_code_preserving", "positive_pattern": "codigo|codpos|\\bcep\\b", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ru": [{"pattern_identifier": "ru_zip_code_preserving", "positive_pattern": "Почтовый.?Индекс", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "hi": [{"pattern_identifier": "hi_zip_code_preserving", "positive_pattern": "पिन.?कोड", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ml": [{"pattern_identifier": "ml_zip_code_preserving", "positive_pattern": "പിന്‍കോഡ്", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "zh-CN": [{"pattern_identifier": "zh_zip_code_preserving", "positive_pattern": "邮政编码|邮编", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "zh-TW": [{"pattern_identifier": "zh_tw_zip_code_preserving", "positive_pattern": "郵遞區號", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "tr": [{"pattern_identifier": "tr_zip_code_preserving", "positive_pattern": "(\\b|_)posta kodu(\\b|_)", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ko": [{"pattern_identifier": "ko_zip_code_preserving", "positive_pattern": "우편.?번호", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "id": [{"pattern_identifier": "id_zip_code_preserving", "positive_pattern": "kode.?pos", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "ZIP_4": {"en": [{"pattern_identifier": "en_zip_4_preserving", "positive_pattern": "zip|^-$|post2", "positive_score": 1.1, "negative_pattern": "\\.zip", "negative_patterns_explainer": ".zip refers to a file extension", "match_field_attributes": 3, "match_field_input_types": 69}], "pt": [{"pattern_identifier": "pt_zip_4_preserving", "positive_pattern": "codpos2", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "ADDRESS_HOME_DEPENDENT_LOCALITY": {"en": [{"pattern_identifier": "en_dependent_locality_preserving", "positive_pattern": "neighbo(u)?rhood", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "pt": [{"pattern_identifier": "pt_dependent_locality_preserving", "positive_pattern": "bairro", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "tr": [{"pattern_identifier": "tr_dependent_locality_preserving", "positive_pattern": "mahalle|köy", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "id": [{"pattern_identifier": "id_dependent_locality_preserving", "positive_pattern": "kecamatan", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}]}, "CITY": {"en": [{"pattern_identifier": "en_city_preserving", "positive_pattern": "city|town|suburb", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "de": [{"pattern_identifier": "de_city_preserving", "positive_pattern": "\\bort\\b|stadt", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "es": [{"pattern_identifier": "es_city_preserving", "positive_pattern": "ciudad|provincia|localidad|poblacion", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "fr": [{"pattern_identifier": "fr_city_preserving", "positive_pattern": "ville|commune", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "it": [{"pattern_identifier": "it_city_preserving", "positive_pattern": "localita", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ja": [{"pattern_identifier": "ja_city_preserving", "positive_pattern": "市区町村", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "pt": [{"pattern_identifier": "pt_city_preserving", "positive_pattern": "cidade|município", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ru": [{"pattern_identifier": "ru_city_preserving", "positive_pattern": "Город|Насел(е|ё)нный.?пункт", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "zh-TW": [{"pattern_identifier": "zh_city_preserving", "positive_pattern": "市|分區", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "fa": [{"pattern_identifier": "fa_city_preserving", "positive_pattern": "شهر", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "hi": [{"pattern_identifier": "hi_city_preserving", "positive_pattern": "शहर|ग्राम|गाँव", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ml": [{"pattern_identifier": "ml_city_preserving", "positive_pattern": "നഗരം|ഗ്രാമം", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "tr": [{"pattern_identifier": "tr_city_preserving", "positive_pattern": "((\\b|_|\\*)([İii̇]l[cç]e(miz|niz)?)(\\b|_|\\*))", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ko": [{"pattern_identifier": "ko_city_preserving", "positive_pattern": "^시[^도·・]|시[·・]?군[·・]?구", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "id": [{"pattern_identifier": "id_city_preserving", "positive_pattern": "kota|kabupaten", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}]}, "STATE": {"en": [{"pattern_identifier": "en_state_preserving", "positive_pattern": "(?<!(united|hist|history).?)state|county|region|province|county|principality", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ja": [{"pattern_identifier": "ja_state_preserving", "positive_pattern": "都道府県", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "pt": [{"pattern_identifier": "pt_state_preserving", "positive_pattern": "estado|provincia", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ru": [{"pattern_identifier": "ru_state_preserving", "positive_pattern": "область", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "zh-TW": [{"pattern_identifier": "zh_state_preserving", "positive_pattern": "省|地區", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ml": [{"pattern_identifier": "ml_state_preserving", "positive_pattern": "സംസ്ഥാനം", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "fa": [{"pattern_identifier": "fa_state_preserving", "positive_pattern": "استان", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "hi": [{"pattern_identifier": "hi_state_preserving", "positive_pattern": "राज<PERSON>य", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "tr": [{"pattern_identifier": "tr_state_preserving", "positive_pattern": "((\\b|_|\\*)(eyalet|[şs]ehir|[İii̇]l(imiz)?|kent)(\\b|_|\\*))", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ko": [{"pattern_identifier": "ko_state_preserving", "positive_pattern": "^시[·・]?도", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "id": [{"pattern_identifier": "id_state_preserving", "positive_pattern": "provinci", "positive_score": 1.1, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}]}, "SEARCH_TERM": {"en": [{"pattern_identifier": "en_search_term_preserving", "positive_pattern": "^q$|search|query|qry", "positive_score": 0.8, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 145}], "de": [{"pattern_identifier": "de_search_term_preserving", "positive_pattern": "suche.*", "positive_score": 0.8, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 145}], "zh-CN": [{"pattern_identifier": "zh_search_term_preserving", "positive_pattern": "搜索", "positive_score": 0.8, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 145}], "ja": [{"pattern_identifier": "ja_search_term_preserving", "positive_pattern": "探す|検索", "positive_score": 0.8, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 145}], "fr": [{"pattern_identifier": "fr_search_term_preserving", "positive_pattern": "recherch.*", "positive_score": 0.8, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 145}], "pt": [{"pattern_identifier": "pt_search_term_preserving", "positive_pattern": "busca", "positive_score": 0.8, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 145}], "fa": [{"pattern_identifier": "fa_search_term_preserving", "positive_pattern": "جستجو", "positive_score": 0.8, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 145}], "ru": [{"pattern_identifier": "ru_search_term_preserving", "positive_pattern": "искать|найти|поиск", "positive_score": 0.8, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 145}]}, "PRICE": {"en": [{"pattern_identifier": "en_price_preserving", "positive_pattern": "\\bprice\\b|\\brate\\b|\\bcost\\b", "positive_score": 0.95, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 217}], "ar": [{"pattern_identifier": "ar_price_preserving", "positive_pattern": "قیمة‎|سعر‎", "positive_score": 0.95, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 217}], "fa": [{"pattern_identifier": "fa_price_preserving", "positive_pattern": "قیمت", "positive_score": 0.95, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 217}], "fr": [{"pattern_identifier": "fr_price_preserving", "positive_pattern": "\\bprix\\b|\\bcoût\\b|\\bcout\\b|\\btarif\\b", "positive_score": 0.95, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 217}]}, "NAME_ON_CARD": {"en": [{"pattern_identifier": "en_name_on_card_preserving", "positive_pattern": "card.?(?:holder|owner)|name.*(\\b)?on(\\b)?.*card|(?:card|cc).?name|cc.?full.?name", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "de": [{"pattern_identifier": "de_name_on_card_preserving", "positive_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_name_on_card_preserving", "positive_pattern": "nombre.*tarjeta", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fr": [{"pattern_identifier": "fr_name_on_card_preserving", "positive_pattern": "nom.*carte", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "it": [{"pattern_identifier": "it_name_on_card_preserving", "positive_pattern": "nome.*cart", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ja": [{"pattern_identifier": "ja_name_on_card_preserving", "positive_pattern": "名前", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ru": [{"pattern_identifier": "ru_name_on_card_preserving", "positive_pattern": "Имя.*карты", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "zh-CN": [{"pattern_identifier": "zh_name_on_card_preserving", "positive_pattern": "信用卡开户名|开户名|持卡人姓名|持卡人姓名", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "id": [{"pattern_identifier": "id_name_on_card_preserving", "positive_pattern": "nama.*kartu", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "NAME_ON_CARD_CONTEXTUAL": {"en": [{"pattern_identifier": "en_name_on_card_contextual_preserving", "positive_pattern": "name", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "CREDIT_CARD_NUMBER": {"en": [{"pattern_identifier": "en_card_number_preserving", "positive_pattern": "(add)?(?:card|cc|acct).?(?:number|#|no|num|field|pan)", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}], "de": [{"pattern_identifier": "de_card_number_preserving", "positive_pattern": "(?<!telefon|haus|person|føds<PERSON>|kunden)nummer", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}], "ja": [{"pattern_identifier": "ja_card_number_preserving", "positive_pattern": "カード番号", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}], "ru": [{"pattern_identifier": "ru_card_number_preserving", "positive_pattern": "Номер.*карты", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}], "zh-CN": [{"pattern_identifier": "zh_card_number_preserving", "positive_pattern": "信用卡号|信用卡号码", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}], "zh-TW": [{"pattern_identifier": "zh_card_number_preserving", "positive_pattern": "信用卡卡號", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}], "ko": [{"pattern_identifier": "ko_card_number_preserving", "positive_pattern": "카드", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}], "es": [{"pattern_identifier": "es_card_number_preserving", "positive_pattern": "(numero|número|numéro)(?!.*(document|fono|phone|réservation))", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}], "pt": [{"pattern_identifier": "pt_card_number_preserving", "positive_pattern": "(numero|número|numéro)(?!.*(document|fono|phone|réservation))", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}], "fr": [{"pattern_identifier": "fr_card_number_preserving", "positive_pattern": "(numero|número|numéro)(?!.*(document|fono|phone|réservation))", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}], "id": [{"pattern_identifier": "id_card_number_preserving", "positive_pattern": "no.*kartu", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}]}, "CREDIT_CARD_VERIFICATION_CODE": {"en": [{"pattern_identifier": "en_card_cvc_preserving", "positive_pattern": "verification|card.?identification|security.?code|card.?code|security.?value|security.?number|card.?pin|c-v-v|(cvn|cvv|cvc|csc|cvd|cid|ccv)(field)?|\\bcid\\b", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 101}]}, "CREDIT_CARD_EXP_MONTH": {"en": [{"pattern_identifier": "en_card_exp_month_preserving", "positive_pattern": "expir|exp.*mo|exp.*date|ccmonth|cardmonth|addmonth", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "de": [{"pattern_identifier": "de_card_exp_month_preserving", "positive_pattern": "gueltig|gültig|monat", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "es": [{"pattern_identifier": "es_card_exp_month_preserving", "positive_pattern": "fecha", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "fr": [{"pattern_identifier": "fr_card_exp_month_preserving", "positive_pattern": "date.*exp", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "it": [{"pattern_identifier": "it_card_exp_month_preserving", "positive_pattern": "scadenza", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "ja": [{"pattern_identifier": "ja_card_exp_month_preserving", "positive_pattern": "有効期限", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "pt": [{"pattern_identifier": "pt_card_exp_month_preserving", "positive_pattern": "validade", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "ru": [{"pattern_identifier": "ru_card_exp_month_preserving", "positive_pattern": "Срок действия карты", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "zh-CN": [{"pattern_identifier": "zh_card_exp_month_preserving", "positive_pattern": "月", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "id": [{"pattern_identifier": "id_card_exp_month_preserving", "positive_pattern": "masa berlaku|berlaku hingga", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}]}, "CREDIT_CARD_EXP_YEAR": {"en": [{"pattern_identifier": "en_card_exp_year_preserving", "positive_pattern": "exp|^/|(add)?year", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "de": [{"pattern_identifier": "de_card_exp_year_preserving", "positive_pattern": "ablaufdatum|gueltig|g<PERSON><PERSON>ig|jahr", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "es": [{"pattern_identifier": "es_card_exp_year_preserving", "positive_pattern": "fecha", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "it": [{"pattern_identifier": "it_card_exp_year_preserving", "positive_pattern": "scadenza", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "ja": [{"pattern_identifier": "ja_card_exp_year_preserving", "positive_pattern": "有効期限", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "pt": [{"pattern_identifier": "pt_card_exp_year_preserving", "positive_pattern": "validade", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "ru": [{"pattern_identifier": "ru_card_exp_year_preserving", "positive_pattern": "Срок действия карты", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "zh-CN": [{"pattern_identifier": "zh_card_exp_year_preserving", "positive_pattern": "年|有效期", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}]}, "CREDIT_CARD_EXP_DATE_2_DIGIT_YEAR": {"en": [{"pattern_identifier": "en_card_exp_date_2_digit_year_preserving", "positive_pattern": "(?:exp.*date[^y\\n\\r]*|mm\\s*[-/]?\\s*)yy(?:[^y]|$)", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}]}, "CREDIT_CARD_EXP_DATE_4_DIGIT_YEAR": {"en": [{"pattern_identifier": "en_card_exp_date_4_digit_year_preserving", "positive_pattern": "(?:exp.*date[^y\\n\\r]*|mm\\s*[-/]?\\s*)yyyy(?:[^y]|$)", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}]}, "CREDIT_CARD_EXP_DATE": {"en": [{"pattern_identifier": "en_card_exp_date_preserving", "positive_pattern": "expir|exp.*date|^expfield$", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "de": [{"pattern_identifier": "de_card_exp_date_preserving", "positive_pattern": "gueltig|gültig", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "es": [{"pattern_identifier": "es_card_exp_date_preserving", "positive_pattern": "fecha", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "fr": [{"pattern_identifier": "fr_card_exp_date_preserving", "positive_pattern": "date.*exp", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "it": [{"pattern_identifier": "it_card_exp_date_preserving", "positive_pattern": "scadenza", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "ja": [{"pattern_identifier": "ja_card_exp_date_preserving", "positive_pattern": "有効期限", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "pt": [{"pattern_identifier": "pt_card_exp_date_preserving", "positive_pattern": "validade", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "ru": [{"pattern_identifier": "ru_card_exp_date_preserving", "positive_pattern": "Срок действия карты", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}], "id": [{"pattern_identifier": "id_card_exp_date_preserving", "positive_pattern": "masa berlaku|berlaku hingga", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}]}, "CREDIT_CARD_EXP_MONTH_BEFORE_YEAR": {"en": [{"pattern_identifier": "en_card_exp_month_before_year_preserving", "positive_pattern": "^mm$", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}]}, "CREDIT_CARD_EXP_YEAR_AFTER_MONTH": {"en": [{"pattern_identifier": "en_card_exp_year_after_month_preserving", "positive_pattern": "^(yy|yyyy)$", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 205}]}, "GIFT_CARD": {"en": [{"pattern_identifier": "en_gift_card_preserving", "positive_pattern": "gift.?(card|cert)", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 197}]}, "DEBIT_GIFT_CARD": {"en": [{"pattern_identifier": "en_debit_gift_card_preserving", "positive_pattern": "(?:visa|mastercard|discover|amex|american express).*gift.?card", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 197}]}, "DEBIT_CARD": {"en": [{"pattern_identifier": "en_debit_card_preserving", "positive_pattern": "debit.*card", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 197}]}, "DAY": {"en": [{"pattern_identifier": "en_day_preserving", "positive_pattern": "day", "positive_score": 1.0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 9}]}, "EMAIL_ADDRESS": {"en": [{"pattern_identifier": "en_email_preserving", "positive_pattern": "e.?mail", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "fr": [{"pattern_identifier": "fr_email_preserving", "positive_pattern": "<PERSON><PERSON><PERSON>", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "es": [{"pattern_identifier": "es_email_preserving", "positive_pattern": "correo.*electr(o|ó)nico", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "ja": [{"pattern_identifier": "ja_email_preserving", "positive_pattern": "メールアドレス", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "ru": [{"pattern_identifier": "ru_email_preserving", "positive_pattern": "Электронн(ая|ой).?Почт(а|ы)", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "zh-CN": [{"pattern_identifier": "zh_email_preserving", "positive_pattern": "邮件|邮箱", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "zh-TW": [{"pattern_identifier": "zh_email_preserving", "positive_pattern": "電郵地址|電子信箱", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "ml": [{"pattern_identifier": "ml_email_preserving", "positive_pattern": "ഇ-മെയില്‍|ഇലക്ട്രോണിക്.?മെയിൽ", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "fa": [{"pattern_identifier": "fa_email_preserving", "positive_pattern": "ایمیل|پست.*الکترونیک", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "hi": [{"pattern_identifier": "hi_email_preserving", "positive_pattern": "ईमेल|इलॅक्ट्रॉनिक.?मेल", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "tr": [{"pattern_identifier": "tr_email_preserving", "positive_pattern": "(\\b|_)eposta(\\b|_)", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}], "ko": [{"pattern_identifier": "ko_email_preserving", "positive_pattern": "(?:이메일|전자.?우편|[Ee]-?mail)(.?주소)?", "positive_score": 1.4, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 3}]}, "NAME_IGNORED": {"en": [{"pattern_identifier": "en_name_ignored_preserving", "positive_pattern": "user.?name|user.?id|nickname|maiden name|title|prefix|suffix", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "de": [{"pattern_identifier": "de_name_ignored_preserving", "positive_pattern": "vollständiger.?name", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "zh-CN": [{"pattern_identifier": "zh_name_ignored_preserving", "positive_pattern": "用户名", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}], "ko": [{"pattern_identifier": "ko_name_ignored_preserving", "positive_pattern": "(?:사용자.?)?아이디|사용자.?ID", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 137}]}, "FULL_NAME": {"en": [{"pattern_identifier": "en_full_name_preserving", "positive_pattern": "^name|full.?name|your.?name|customer.?name|bill.?name|ship.?name|name.*first.*last|firstandlastname|contact.?(name|person)", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_full_name_preserving", "positive_pattern": "nombre.*y.*a<PERSON><PERSON><PERSON>", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fr": [{"pattern_identifier": "fr_full_name_preserving", "positive_pattern": "^nom(?![a-zA-Z])", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ja": [{"pattern_identifier": "ja_full_name_preserving", "positive_pattern": "お名前|氏名", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "pt": [{"pattern_identifier": "pt_full_name_preserving", "positive_pattern": "^nome", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fa": [{"pattern_identifier": "fa_full_name_preserving", "positive_pattern": "نام.*نام.*خانوادگی", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "zh-CN": [{"pattern_identifier": "zh_full_name_preserving", "positive_pattern": "姓名", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ru": [{"pattern_identifier": "ru_full_name_preserving", "positive_pattern": "контактное.?лицо", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "tr": [{"pattern_identifier": "tr_full_name_preserving", "positive_pattern": "(\\b|_|\\*)ad[ı]? soyad[ı]?(\\b|_|\\*)", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ko": [{"pattern_identifier": "ko_full_name_preserving", "positive_pattern": "성명", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "id": [{"pattern_identifier": "id_full_name_preserving", "positive_pattern": "nama.?(lengkap|penerima|kamu)", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "NAME_SPECIFIC": {"en": [{"pattern_identifier": "en_name_specific_preserving", "positive_pattern": "^name", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fr": [{"pattern_identifier": "fr_name_specific_preserving", "positive_pattern": "^nom", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "pt": [{"pattern_identifier": "pt_name_specific_preserving", "positive_pattern": "^nome", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "FIRST_NAME": {"en": [{"pattern_identifier": "en_first_name_preserving", "positive_pattern": "first.*name|initials|fname|first$|given.*name", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "de": [{"pattern_identifier": "de_first_name_preserving", "positive_pattern": "vorname", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_first_name_preserving", "positive_pattern": "nombre", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fr": [{"pattern_identifier": "fr_first_name_preserving", "positive_pattern": "forename|prénom|prenom", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ja": [{"pattern_identifier": "ja_first_name_preserving", "positive_pattern": "名", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "pt": [{"pattern_identifier": "pt_first_name_preserving", "positive_pattern": "nome", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ru": [{"pattern_identifier": "ru_first_name_preserving", "positive_pattern": "Имя", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fa": [{"pattern_identifier": "fa_first_name_preserving", "positive_pattern": "نام", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ko": [{"pattern_identifier": "ko_first_name_preserving", "positive_pattern": "이름", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ml": [{"pattern_identifier": "ml_first_name_preserving", "positive_pattern": "പേര്", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "tr": [{"pattern_identifier": "tr_first_name_preserving", "positive_pattern": "(\\b|_|\\*)(isim|ad|ad(i|ı|iniz|ınız)?)(\\b|_|\\*)", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "hi": [{"pattern_identifier": "hi_first_name_preserving", "positive_pattern": "नाम", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "id": [{"pattern_identifier": "id_first_name_preserving", "positive_pattern": "nama depan", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "MIDDLE_INITIAL": {"en": [{"pattern_identifier": "en_middle_initial_preserving", "positive_pattern": "middle.*initial|m\\.i\\.|mi$|\\bmi\\b", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "MIDDLE_NAME": {"en": [{"pattern_identifier": "en_middle_name_preserving", "positive_pattern": "middle.*name|mname|middle$", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "LAST_NAME": {"en": [{"pattern_identifier": "en_last_name_preserving", "positive_pattern": "last.*name|lname|surname(?!\\d)|last$|secondname|family.*name", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "de": [{"pattern_identifier": "de_last_name_preserving", "positive_pattern": "nachname", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_last_name_preserving", "positive_pattern": "apel<PERSON><PERSON>?", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fr": [{"pattern_identifier": "fr_last_name_preserving", "positive_pattern": "famille|^nom(?![a-zA-Z])", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "it": [{"pattern_identifier": "it_last_name_preserving", "positive_pattern": "cognome", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ja": [{"pattern_identifier": "ja_last_name_preserving", "positive_pattern": "姓", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "pt": [{"pattern_identifier": "pt_last_name_preserving", "positive_pattern": "apelidos|surename|sobrenome", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ru": [{"pattern_identifier": "ru_last_name_preserving", "positive_pattern": "Фамилия", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fa": [{"pattern_identifier": "fa_last_name_preserving", "positive_pattern": "نام.*خانوادگی", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "hi": [{"pattern_identifier": "hi_last_name_preserving", "positive_pattern": "उपनाम", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ml": [{"pattern_identifier": "ml_last_name_preserving", "positive_pattern": "മറുപേര്", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "tr": [{"pattern_identifier": "tr_last_name_preserving", "positive_pattern": "(\\b|_|\\*)(soyisim|soyad(i|ı|iniz|ınız)?)(\\b|_|\\*)", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ko": [{"pattern_identifier": "ko_last_name_preserving", "positive_pattern": "\\b성(?:[^명]|\\b)", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "id": [{"pattern_identifier": "id_last_name_preserving", "positive_pattern": "nama belakang", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "LAST_NAME_FIRST": {"es": [{"pattern_identifier": "es_last_name_first_preserving", "positive_pattern": "(primer.*apellido)|(apellido1)|(apellido.*paterno)|surname_?1|first(\\s|_)?surname", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "LAST_NAME_SECOND": {"es": [{"pattern_identifier": "es_last_name_second_preserving", "positive_pattern": "(segund.*apellido)|(apellido2)|(apellido.*materno)|surname_?2|second(\\s|_)?surname", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "HONORIFIC_PREFIX": {"en": [{"pattern_identifier": "en_honorific_prefix_preserving", "positive_pattern": "^title:?$|(salutation(?! and given name))", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "de": [{"pattern_identifier": "de_honorific_prefix_preserving", "positive_pattern": "anrede|titel", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_honorific_prefix_preserving", "positive_pattern": "tratamiento|encabezamiento", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "it": [{"pattern_identifier": "it_honorific_prefix_preserving", "positive_pattern": "titolo", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fr": [{"pattern_identifier": "fr_honorific_prefix_preserving", "positive_pattern": "titre", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ru": [{"pattern_identifier": "ru_honorific_prefix_preserving", "positive_pattern": "обращение|звание", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "el": [{"pattern_identifier": "el_honorific_prefix_preserving", "positive_pattern": "προσφώνηση", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "tr": [{"pattern_identifier": "tr_honorific_prefix_preserving", "positive_pattern": "hitap", "positive_score": 0.9, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "PHONE": {"en": [{"pattern_identifier": "en_phone_preserving", "positive_pattern": "phone|mobile|contact.?number", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "de": [{"pattern_identifier": "de_phone_preserving", "positive_pattern": "telefonnummer", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "es": [{"pattern_identifier": "es_phone_preserving", "positive_pattern": "telefono|teléfono", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "fr": [{"pattern_identifier": "fr_phone_preserving", "positive_pattern": "telfixe", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ja": [{"pattern_identifier": "ja_phone_preserving", "positive_pattern": "電話", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "pt": [{"pattern_identifier": "pt_phone_preserving", "positive_pattern": "telefone|telemovel", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ru": [{"pattern_identifier": "ru_phone_preserving", "positive_pattern": "телефон", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "hi": [{"pattern_identifier": "hi_phone_preserving", "positive_pattern": "मोबाइल", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "tr": [{"pattern_identifier": "tr_phone_preserving", "positive_pattern": "(\\b|_|\\*)telefon(\\b|_|\\*)", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "zh-CN": [{"pattern_identifier": "zh_phone_preserving", "positive_pattern": "电话", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ml": [{"pattern_identifier": "ml_phone_preserving", "positive_pattern": "മൊബൈല്‍", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ko": [{"pattern_identifier": "ko_phone_preserving", "positive_pattern": "(?:전화|핸드폰|휴대폰|휴대전화)(?:.?번호)?", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "id": [{"pattern_identifier": "id_phone_preserving", "positive_pattern": "telepon|ponsel|(nomor|no\\.?).?(hp|handphone)", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "AUGMENTED_PHONE_COUNTRY_CODE": {"en": [{"pattern_identifier": "en_augmented_phone_country_code_preserving", "positive_pattern": "^[^0-9+]*(?:\\+|00)\\s*([1-9]\\d{0,3})\\D*$", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "PHONE_COUNTRY_CODE": {"en": [{"pattern_identifier": "en_phone_country_code_preserving", "positive_pattern": "country.*code|ccode|_cc|phone.*code|user.*phone.*code", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 77}]}, "PHONE_AREA_CODE_NO_TEXT": {"en": [{"pattern_identifier": "en_phone_area_code_no_text_preserving", "positive_pattern": "^\\($", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "PHONE_AREA_CODE": {"en": [{"pattern_identifier": "en_phone_area_code_preserving", "positive_pattern": "area.*code|acode|area", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "ko": [{"pattern_identifier": "ko_phone_area_code_preserving", "positive_pattern": "지역.?번호", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "PHONE_PREFIX_SEPARATOR": {"en": [{"pattern_identifier": "en_phone_prefix_separator_preserving", "positive_pattern": "^-$|^\\)$", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "PHONE_SUFFIX_SEPARATOR": {"en": [{"pattern_identifier": "en_phone_suffix_separator_preserving", "positive_pattern": "^-$", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "PHONE_PREFIX": {"en": [{"pattern_identifier": "en_phone_prefix_preserving", "positive_pattern": "prefix|exchange", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "fr": [{"pattern_identifier": "fr_phone_prefix_preserving", "positive_pattern": "preselection", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "pt": [{"pattern_identifier": "pt_phone_prefix_preserving", "positive_pattern": "ddd", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "PHONE_SUFFIX": {"en": [{"pattern_identifier": "en_phone_suffix_preserving", "positive_pattern": "suffix", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "PHONE_EXTENSION": {"en": [{"pattern_identifier": "en_phone_extension_preserving", "positive_pattern": "\\bext|ext\\b|extension", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}], "pt": [{"pattern_identifier": "pt_phone_extension_preserving", "positive_pattern": "<PERSON><PERSON>", "positive_score": 1.3, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 69}]}, "PASSPORT": {"en": [{"pattern_identifier": "en_passport_preserving", "positive_pattern": "document.*number|passport", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "fr": [{"pattern_identifier": "fr_passport_preserving", "positive_pattern": "passeport", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_passport_preserving", "positive_pattern": "numero.*documento|pasaporte", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ja": [{"pattern_identifier": "ja_passport_preserving", "positive_pattern": "書類", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "TRAVEL_ORIGIN": {"en": [{"pattern_identifier": "en_travel_origin_preserving", "positive_pattern": "point.*of.*entry|arrival", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_travel_origin_preserving", "positive_pattern": "punto.*internaci(o|ó)n|fecha.*llegada", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ja": [{"pattern_identifier": "ja_travel_origin_preserving", "positive_pattern": "入国", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "TRAVEL_DESTINATION": {"en": [{"pattern_identifier": "en_travel_destination_preserving", "positive_pattern": "departure", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_travel_destination_preserving", "positive_pattern": "fecha.*salida|destino", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ja": [{"pattern_identifier": "ja_travel_destination_preserving", "positive_pattern": "出国", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "FLIGHT": {"en": [{"pattern_identifier": "en_flight_preserving", "positive_pattern": "airline|flight", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "es": [{"pattern_identifier": "es_flight_preserving", "positive_pattern": "aerol(i|í)nea|n(u|ú)mero.*vuelo", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}], "ja": [{"pattern_identifier": "ja_flight_preserving", "positive_pattern": "便名|航空会社", "positive_score": 1.2, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "UPI_VIRTUAL_PAYMENT_ADDRESS": {"en": [{"pattern_identifier": "en_upi_virtual_payment_address_user@(IFSC/Aadhaar/Mobile/RuPay)_preserving", "positive_pattern": "^[\\w.+-_]+@(\\w+\\.ifsc\\.npci|a<PERSON><PERSON><PERSON>\\.npci|mobile\\.npci|rupay\\.npci)$", "positive_score": 0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}, {"pattern_identifier": "en_upi_virtual_payment_address_user@(bank_list)_preserving", "positive_pattern": "^[\\w.+-_]+@(airtel|airtelpaymentsbank|albk|allahabadbank|allbank|andb|apb|apl|axis|axisbank|axisgo|bandhan|barodampay|birla|boi|cbin|cboi|centralbank|cmsidfc|cnrb|csbcash|csbpay|cub|dbs|dcb|dcbbank|denabank|dlb|eazypay|equitas|ezeepay|fbl|federal|finobank|hdfcbank|hsbc|icici|idbi|idbibank|idfc|idfcbank|idfcnetc|ikwik|imobile|indbank|indianbank|indianbk|indus|iob|jkb|jsb|jsbp|karb|karurvysyabank|kaypay|kbl|kbl052|kmb|kmbl|kotak|kvb|kvbank|lime|lvb|lvbank|mahb|obc|okaxis|okbizaxis|okhdfcbank|okicici|oksbi|paytm|payzapp|pingpay|pnb|pockets|psb|purz|rajgovhdfcbank|rbl|sbi|sc|scb|scbl|scmobile|sib|srcb|synd|syndbank|syndicate|tjsb|tjsp|ubi|uboi|uco|unionbank|unionbankofindia|united|upi|utbi|vijayabank|vijb|vjb|ybl|yesbank|yesbankltd)$", "positive_score": 0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "INTERNATIONAL_BANK_ACCOUNT_NUMBER": {"en": [{"pattern_identifier": "en_international_bank_account_number_preserving", "positive_pattern": "^[a-zA-Z]{2}[0-9]{2}[a-zA-Z0-9]{4}[0-9]{7}([a-zA-Z0-9]?){0,16}$", "positive_score": 0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "VALIDATION_CREDIT_CARD_VERIFICATION_CODE": {"en": [{"pattern_identifier": "en_credit_card_cvc_preserving", "positive_pattern": "^\\d{3,4}$", "positive_score": 0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "VALIDATION_CREDIT_CARD_EXP_YEAR": {"en": [{"pattern_identifier": "en_credit_card_exp_year_preserving", "positive_pattern": "^[2][0][1-9][0-9]$", "positive_score": 0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "URL_SEARCH_ACTION": {"en": [{"pattern_identifier": "en_url_search_action_preserving", "positive_pattern": "/search(/|((\\w*\\.\\w+)?$))", "positive_score": 0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "SOCIAL_SECURITY": {"en": [{"pattern_identifier": "en_social_security_preserving", "positive_pattern": "ssn|social.?security.?(num(ber)?|#)*", "positive_score": 0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}, "ONE_TIME_PASSWORD": {"en": [{"pattern_identifier": "en_one_time_password_preserving", "positive_pattern": "one.?time|sms.?(code|token|password|pwd|pass)", "positive_score": 0, "negative_pattern": null, "match_field_attributes": 3, "match_field_input_types": 1}]}}
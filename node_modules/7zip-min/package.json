{"name": "7zip-min", "version": "1.4.5", "description": "Standalone cross-platform zip/unzip with 7za", "main": "index.js", "scripts": {"release": "np --no-yarn --no-cleanup", "preview": "np --no-yarn --no-cleanup --preview", "test": "ava"}, "repository": {"type": "git", "url": "https://github.com/onikienko/7zip-min.git"}, "keywords": ["7z", "7zip", "7-zip", "7za", "compress", "decompress", "extract", "lzma", "zip", "gzip", "bzip2", "tar"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/onikienko/7zip-min/issues"}, "homepage": "https://github.com/onikienko/7zip-min#readme", "dependencies": {"7zip-bin": "5.1.1"}, "devDependencies": {"ava": "^4.0.1", "fs-extra": "^11.1.0", "fsify": "^5.0.0", "glob": "^8.0.1", "np": "^7.6.0"}}
{"name": "@nodegui/artifact-installer", "version": "1.1.0", "description": "An artifact or prebuilt binary installer for for nodegui", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "test": "jest --verbose"}, "files": ["dist/"], "author": "<EMAIL>", "license": "ISC", "devDependencies": {"@types/7zip-min": "^1.1.0", "@types/empty-dir": "^2.0.0", "@types/jest": "^25.1.2", "@types/node-fetch": "^2.5.4", "@types/progress": "^2.0.3", "@typescript-eslint/eslint-plugin": "^2.19.0", "@typescript-eslint/parser": "^2.19.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "jest": "^25.1.0", "prettier": "^1.19.1", "ts-jest": "^25.2.0", "typescript": "^3.7.5"}, "dependencies": {"7zip-min": "^1.1.1", "env-paths": "^2.2.0", "make-dir": "^3.0.0", "node-fetch": "^2.6.0", "progress": "^2.0.3"}}
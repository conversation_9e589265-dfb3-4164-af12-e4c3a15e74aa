"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var node_fetch_1 = __importDefault(require("node-fetch"));
var util_1 = __importDefault(require("util"));
var fs_1 = __importDefault(require("fs"));
var path_1 = __importDefault(require("path"));
var stream_1 = __importDefault(require("stream"));
var make_dir_1 = __importDefault(require("make-dir"));
var progress_1 = __importDefault(require("progress"));
var streamPipeline = util_1.default.promisify(stream_1.default.pipeline);
var fsExist = util_1.default.promisify(fs_1.default.exists);
function progressBar(tokens, total) {
    var pt = new stream_1.default.PassThrough();
    var bar = new progress_1.default(tokens, { total: total });
    pt.on('data', function (chunk) { return bar.tick(chunk.length); });
    return pt;
}
function download(link, outPath, options) {
    if (options === void 0) { options = {}; }
    return __awaiter(this, void 0, void 0, function () {
        var name, response, total, totalInMb;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    name = options.name || '';
                    return [4, node_fetch_1.default(link)];
                case 1:
                    response = _a.sent();
                    if (!response.ok) {
                        throw new Error("Error while downloading " + name + ":" + link + ". " + response.statusText);
                    }
                    if (!options.skipIfExist) return [3, 3];
                    return [4, fsExist(outPath)];
                case 2:
                    if (_a.sent()) {
                        return [2, console.warn("Archive already exists at " + outPath + ". Skipping download....")];
                    }
                    _a.label = 3;
                case 3: return [4, make_dir_1.default(path_1.default.dirname(outPath))];
                case 4:
                    _a.sent();
                    total = parseInt("" + response.headers.get('content-length'), 10);
                    totalInMb = (total / 1024 / 1024).toFixed(2);
                    return [4, streamPipeline(response.body, progressBar("Downloading " + name + " [:bar] :percent of " + totalInMb + "MB :etas", total), fs_1.default.createWriteStream(outPath))];
                case 5:
                    _a.sent();
                    return [2];
            }
        });
    });
}
exports.download = download;

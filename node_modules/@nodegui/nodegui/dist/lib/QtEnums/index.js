"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MaskMode = exports.LayoutDirection = exports.KeyboardModifier = exports.Key = exports.ItemSelectionOperation = exports.ItemSelectionMode = exports.ItemFlag = exports.ItemDataRole = exports.InputMethodQuery = exports.InputMethodHint = exports.ImageConversionFlag = exports.HitTestAccuracy = exports.GlobalColor = exports.GestureType = exports.GestureState = exports.GestureFlag = exports.FocusReason = exports.FocusPolicy = exports.FindChildOption = exports.FillRule = exports.FileMode = exports.EventPriority = exports.EnterKeyType = exports.Edge = exports.DropAction = exports.DockWidgetArea = exports.Direction = exports.DialogLabel = exports.DayOfWeek = exports.DateFormat = exports.CursorShape = exports.CursorMoveStyle = exports.Corner = exports.CoordinateSystem = exports.ContextMenuPolicy = exports.ConnectionType = exports.ClipOperation = exports.ChecksumType = exports.CheckState = exports.CaseSensitivity = exports.BrushStyle = exports.BGMode = exports.Axis = exports.AspectRatioMode = exports.ArrowType = exports.ApplicationState = exports.ApplicationAttribute = exports.AnchorPoint = exports.AlignmentFlag = exports.AcceptMode = void 0;
exports.StackingMode = exports.Visibility = exports.DialogCode = exports.PenCapStyle = exports.PenStyle = exports.WindowType = exports.WindowState = exports.WindowModality = exports.WindowFrameSection = exports.WidgetAttribute = exports.WhiteSpaceMode = exports.ViewMode = exports.UIEffect = exports.TransformationMode = exports.TouchPointState = exports.ToolButtonStyle = exports.TimerType = exports.ToolBarArea = exports.TimeSpec = exports.TileRule = exports.TextInteractionFlag = exports.TextFormat = exports.TextFlag = exports.TextElideMode = exports.TabPosition = exports.TabFocusBehavior = exports.SortOrder = exports.SizeMode = exports.SizeHint = exports.SizeAdjustPolicy = exports.ShortcutContext = exports.ScrollPhase = exports.ScrollHint = exports.ScrollBarPolicy = exports.ScreenOrientation = exports.Orientation = exports.Option = exports.NavigationMode = exports.NativeGestureType = exports.MouseEventSource = exports.MouseEventFlag = exports.MouseButton = exports.Modifier = exports.MatchFlag = void 0;
var AcceptMode_1 = require("./AcceptMode");
Object.defineProperty(exports, "AcceptMode", { enumerable: true, get: function () { return AcceptMode_1.AcceptMode; } });
var AlignmentFlag_1 = require("./AlignmentFlag");
Object.defineProperty(exports, "AlignmentFlag", { enumerable: true, get: function () { return AlignmentFlag_1.AlignmentFlag; } });
var AnchorPoint_1 = require("./AnchorPoint");
Object.defineProperty(exports, "AnchorPoint", { enumerable: true, get: function () { return AnchorPoint_1.AnchorPoint; } });
var ApplicationAttribute_1 = require("./ApplicationAttribute");
Object.defineProperty(exports, "ApplicationAttribute", { enumerable: true, get: function () { return ApplicationAttribute_1.ApplicationAttribute; } });
var ApplicationState_1 = require("./ApplicationState");
Object.defineProperty(exports, "ApplicationState", { enumerable: true, get: function () { return ApplicationState_1.ApplicationState; } });
var ArrowType_1 = require("./ArrowType");
Object.defineProperty(exports, "ArrowType", { enumerable: true, get: function () { return ArrowType_1.ArrowType; } });
var AspectRatioMode_1 = require("./AspectRatioMode");
Object.defineProperty(exports, "AspectRatioMode", { enumerable: true, get: function () { return AspectRatioMode_1.AspectRatioMode; } });
var Axis_1 = require("./Axis");
Object.defineProperty(exports, "Axis", { enumerable: true, get: function () { return Axis_1.Axis; } });
var BGMode_1 = require("./BGMode");
Object.defineProperty(exports, "BGMode", { enumerable: true, get: function () { return BGMode_1.BGMode; } });
var BrushStyle_1 = require("./BrushStyle");
Object.defineProperty(exports, "BrushStyle", { enumerable: true, get: function () { return BrushStyle_1.BrushStyle; } });
var CaseSensitivity_1 = require("./CaseSensitivity");
Object.defineProperty(exports, "CaseSensitivity", { enumerable: true, get: function () { return CaseSensitivity_1.CaseSensitivity; } });
var CheckState_1 = require("./CheckState");
Object.defineProperty(exports, "CheckState", { enumerable: true, get: function () { return CheckState_1.CheckState; } });
var ChecksumType_1 = require("./ChecksumType");
Object.defineProperty(exports, "ChecksumType", { enumerable: true, get: function () { return ChecksumType_1.ChecksumType; } });
var ClipOperation_1 = require("./ClipOperation");
Object.defineProperty(exports, "ClipOperation", { enumerable: true, get: function () { return ClipOperation_1.ClipOperation; } });
var ConnectionType_1 = require("./ConnectionType");
Object.defineProperty(exports, "ConnectionType", { enumerable: true, get: function () { return ConnectionType_1.ConnectionType; } });
var ContextMenuPolicy_1 = require("./ContextMenuPolicy");
Object.defineProperty(exports, "ContextMenuPolicy", { enumerable: true, get: function () { return ContextMenuPolicy_1.ContextMenuPolicy; } });
var CoordinateSystem_1 = require("./CoordinateSystem");
Object.defineProperty(exports, "CoordinateSystem", { enumerable: true, get: function () { return CoordinateSystem_1.CoordinateSystem; } });
var Corner_1 = require("./Corner");
Object.defineProperty(exports, "Corner", { enumerable: true, get: function () { return Corner_1.Corner; } });
var CursorMoveStyle_1 = require("./CursorMoveStyle");
Object.defineProperty(exports, "CursorMoveStyle", { enumerable: true, get: function () { return CursorMoveStyle_1.CursorMoveStyle; } });
var CursorShape_1 = require("./CursorShape");
Object.defineProperty(exports, "CursorShape", { enumerable: true, get: function () { return CursorShape_1.CursorShape; } });
var DateFormat_1 = require("./DateFormat");
Object.defineProperty(exports, "DateFormat", { enumerable: true, get: function () { return DateFormat_1.DateFormat; } });
var DayOfWeek_1 = require("./DayOfWeek");
Object.defineProperty(exports, "DayOfWeek", { enumerable: true, get: function () { return DayOfWeek_1.DayOfWeek; } });
var DialogLabel_1 = require("./DialogLabel");
Object.defineProperty(exports, "DialogLabel", { enumerable: true, get: function () { return DialogLabel_1.DialogLabel; } });
var Direction_1 = require("./Direction");
Object.defineProperty(exports, "Direction", { enumerable: true, get: function () { return Direction_1.Direction; } });
var DockWidgetArea_1 = require("./DockWidgetArea");
Object.defineProperty(exports, "DockWidgetArea", { enumerable: true, get: function () { return DockWidgetArea_1.DockWidgetArea; } });
var DropAction_1 = require("./DropAction");
Object.defineProperty(exports, "DropAction", { enumerable: true, get: function () { return DropAction_1.DropAction; } });
var Edge_1 = require("./Edge");
Object.defineProperty(exports, "Edge", { enumerable: true, get: function () { return Edge_1.Edge; } });
var EnterKeyType_1 = require("./EnterKeyType");
Object.defineProperty(exports, "EnterKeyType", { enumerable: true, get: function () { return EnterKeyType_1.EnterKeyType; } });
var EventPriority_1 = require("./EventPriority");
Object.defineProperty(exports, "EventPriority", { enumerable: true, get: function () { return EventPriority_1.EventPriority; } });
var FileMode_1 = require("./FileMode");
Object.defineProperty(exports, "FileMode", { enumerable: true, get: function () { return FileMode_1.FileMode; } });
var FillRule_1 = require("./FillRule");
Object.defineProperty(exports, "FillRule", { enumerable: true, get: function () { return FillRule_1.FillRule; } });
var FindChildOption_1 = require("./FindChildOption");
Object.defineProperty(exports, "FindChildOption", { enumerable: true, get: function () { return FindChildOption_1.FindChildOption; } });
var FocusPolicy_1 = require("./FocusPolicy");
Object.defineProperty(exports, "FocusPolicy", { enumerable: true, get: function () { return FocusPolicy_1.FocusPolicy; } });
var FocusReason_1 = require("./FocusReason");
Object.defineProperty(exports, "FocusReason", { enumerable: true, get: function () { return FocusReason_1.FocusReason; } });
var GestureFlag_1 = require("./GestureFlag");
Object.defineProperty(exports, "GestureFlag", { enumerable: true, get: function () { return GestureFlag_1.GestureFlag; } });
var GestureState_1 = require("./GestureState");
Object.defineProperty(exports, "GestureState", { enumerable: true, get: function () { return GestureState_1.GestureState; } });
var GestureType_1 = require("./GestureType");
Object.defineProperty(exports, "GestureType", { enumerable: true, get: function () { return GestureType_1.GestureType; } });
var GlobalColor_1 = require("./GlobalColor");
Object.defineProperty(exports, "GlobalColor", { enumerable: true, get: function () { return GlobalColor_1.GlobalColor; } });
var HitTestAccuracy_1 = require("./HitTestAccuracy");
Object.defineProperty(exports, "HitTestAccuracy", { enumerable: true, get: function () { return HitTestAccuracy_1.HitTestAccuracy; } });
var ImageConversionFlag_1 = require("./ImageConversionFlag");
Object.defineProperty(exports, "ImageConversionFlag", { enumerable: true, get: function () { return ImageConversionFlag_1.ImageConversionFlag; } });
var InputMethodHint_1 = require("./InputMethodHint");
Object.defineProperty(exports, "InputMethodHint", { enumerable: true, get: function () { return InputMethodHint_1.InputMethodHint; } });
var InputMethodQuery_1 = require("./InputMethodQuery");
Object.defineProperty(exports, "InputMethodQuery", { enumerable: true, get: function () { return InputMethodQuery_1.InputMethodQuery; } });
var ItemDataRole_1 = require("./ItemDataRole");
Object.defineProperty(exports, "ItemDataRole", { enumerable: true, get: function () { return ItemDataRole_1.ItemDataRole; } });
var ItemFlag_1 = require("./ItemFlag");
Object.defineProperty(exports, "ItemFlag", { enumerable: true, get: function () { return ItemFlag_1.ItemFlag; } });
var ItemSelectionMode_1 = require("./ItemSelectionMode");
Object.defineProperty(exports, "ItemSelectionMode", { enumerable: true, get: function () { return ItemSelectionMode_1.ItemSelectionMode; } });
var ItemSelectionOperation_1 = require("./ItemSelectionOperation");
Object.defineProperty(exports, "ItemSelectionOperation", { enumerable: true, get: function () { return ItemSelectionOperation_1.ItemSelectionOperation; } });
var Key_1 = require("./Key");
Object.defineProperty(exports, "Key", { enumerable: true, get: function () { return Key_1.Key; } });
var KeyboardModifier_1 = require("./KeyboardModifier");
Object.defineProperty(exports, "KeyboardModifier", { enumerable: true, get: function () { return KeyboardModifier_1.KeyboardModifier; } });
var LayoutDirection_1 = require("./LayoutDirection");
Object.defineProperty(exports, "LayoutDirection", { enumerable: true, get: function () { return LayoutDirection_1.LayoutDirection; } });
var MaskMode_1 = require("./MaskMode");
Object.defineProperty(exports, "MaskMode", { enumerable: true, get: function () { return MaskMode_1.MaskMode; } });
var MatchFlag_1 = require("./MatchFlag");
Object.defineProperty(exports, "MatchFlag", { enumerable: true, get: function () { return MatchFlag_1.MatchFlag; } });
var Modifier_1 = require("./Modifier");
Object.defineProperty(exports, "Modifier", { enumerable: true, get: function () { return Modifier_1.Modifier; } });
var MouseButton_1 = require("./MouseButton");
Object.defineProperty(exports, "MouseButton", { enumerable: true, get: function () { return MouseButton_1.MouseButton; } });
var MouseEventFlag_1 = require("./MouseEventFlag");
Object.defineProperty(exports, "MouseEventFlag", { enumerable: true, get: function () { return MouseEventFlag_1.MouseEventFlag; } });
var MouseEventSource_1 = require("./MouseEventSource");
Object.defineProperty(exports, "MouseEventSource", { enumerable: true, get: function () { return MouseEventSource_1.MouseEventSource; } });
var NativeGestureType_1 = require("./NativeGestureType");
Object.defineProperty(exports, "NativeGestureType", { enumerable: true, get: function () { return NativeGestureType_1.NativeGestureType; } });
var NavigationMode_1 = require("./NavigationMode");
Object.defineProperty(exports, "NavigationMode", { enumerable: true, get: function () { return NavigationMode_1.NavigationMode; } });
var Option_1 = require("./Option");
Object.defineProperty(exports, "Option", { enumerable: true, get: function () { return Option_1.Option; } });
var Orientation_1 = require("./Orientation");
Object.defineProperty(exports, "Orientation", { enumerable: true, get: function () { return Orientation_1.Orientation; } });
var ScreenOrientation_1 = require("./ScreenOrientation");
Object.defineProperty(exports, "ScreenOrientation", { enumerable: true, get: function () { return ScreenOrientation_1.ScreenOrientation; } });
var ScrollBarPolicy_1 = require("./ScrollBarPolicy");
Object.defineProperty(exports, "ScrollBarPolicy", { enumerable: true, get: function () { return ScrollBarPolicy_1.ScrollBarPolicy; } });
var ScrollHint_1 = require("./ScrollHint");
Object.defineProperty(exports, "ScrollHint", { enumerable: true, get: function () { return ScrollHint_1.ScrollHint; } });
var ScrollPhase_1 = require("./ScrollPhase");
Object.defineProperty(exports, "ScrollPhase", { enumerable: true, get: function () { return ScrollPhase_1.ScrollPhase; } });
var ShortcutContext_1 = require("./ShortcutContext");
Object.defineProperty(exports, "ShortcutContext", { enumerable: true, get: function () { return ShortcutContext_1.ShortcutContext; } });
var SizeAdjustPolicy_1 = require("./SizeAdjustPolicy");
Object.defineProperty(exports, "SizeAdjustPolicy", { enumerable: true, get: function () { return SizeAdjustPolicy_1.SizeAdjustPolicy; } });
var SizeHint_1 = require("./SizeHint");
Object.defineProperty(exports, "SizeHint", { enumerable: true, get: function () { return SizeHint_1.SizeHint; } });
var SizeMode_1 = require("./SizeMode");
Object.defineProperty(exports, "SizeMode", { enumerable: true, get: function () { return SizeMode_1.SizeMode; } });
var SortOrder_1 = require("./SortOrder");
Object.defineProperty(exports, "SortOrder", { enumerable: true, get: function () { return SortOrder_1.SortOrder; } });
var TabFocusBehavior_1 = require("./TabFocusBehavior");
Object.defineProperty(exports, "TabFocusBehavior", { enumerable: true, get: function () { return TabFocusBehavior_1.TabFocusBehavior; } });
var TabPosition_1 = require("./TabPosition");
Object.defineProperty(exports, "TabPosition", { enumerable: true, get: function () { return TabPosition_1.TabPosition; } });
var TextElideMode_1 = require("./TextElideMode");
Object.defineProperty(exports, "TextElideMode", { enumerable: true, get: function () { return TextElideMode_1.TextElideMode; } });
var TextFlag_1 = require("./TextFlag");
Object.defineProperty(exports, "TextFlag", { enumerable: true, get: function () { return TextFlag_1.TextFlag; } });
var TextFormat_1 = require("./TextFormat");
Object.defineProperty(exports, "TextFormat", { enumerable: true, get: function () { return TextFormat_1.TextFormat; } });
var TextInteractionFlag_1 = require("./TextInteractionFlag");
Object.defineProperty(exports, "TextInteractionFlag", { enumerable: true, get: function () { return TextInteractionFlag_1.TextInteractionFlag; } });
var TileRule_1 = require("./TileRule");
Object.defineProperty(exports, "TileRule", { enumerable: true, get: function () { return TileRule_1.TileRule; } });
var TimeSpec_1 = require("./TimeSpec");
Object.defineProperty(exports, "TimeSpec", { enumerable: true, get: function () { return TimeSpec_1.TimeSpec; } });
var ToolBarArea_1 = require("./ToolBarArea");
Object.defineProperty(exports, "ToolBarArea", { enumerable: true, get: function () { return ToolBarArea_1.ToolBarArea; } });
var TimerType_1 = require("./TimerType");
Object.defineProperty(exports, "TimerType", { enumerable: true, get: function () { return TimerType_1.TimerType; } });
var ToolButtonStyle_1 = require("./ToolButtonStyle");
Object.defineProperty(exports, "ToolButtonStyle", { enumerable: true, get: function () { return ToolButtonStyle_1.ToolButtonStyle; } });
var TouchPointState_1 = require("./TouchPointState");
Object.defineProperty(exports, "TouchPointState", { enumerable: true, get: function () { return TouchPointState_1.TouchPointState; } });
var TransformationMode_1 = require("./TransformationMode");
Object.defineProperty(exports, "TransformationMode", { enumerable: true, get: function () { return TransformationMode_1.TransformationMode; } });
var UIEffect_1 = require("./UIEffect");
Object.defineProperty(exports, "UIEffect", { enumerable: true, get: function () { return UIEffect_1.UIEffect; } });
var ViewMode_1 = require("./ViewMode");
Object.defineProperty(exports, "ViewMode", { enumerable: true, get: function () { return ViewMode_1.ViewMode; } });
var WhiteSpaceMode_1 = require("./WhiteSpaceMode");
Object.defineProperty(exports, "WhiteSpaceMode", { enumerable: true, get: function () { return WhiteSpaceMode_1.WhiteSpaceMode; } });
var WidgetAttribute_1 = require("./WidgetAttribute");
Object.defineProperty(exports, "WidgetAttribute", { enumerable: true, get: function () { return WidgetAttribute_1.WidgetAttribute; } });
var WindowFrameSection_1 = require("./WindowFrameSection");
Object.defineProperty(exports, "WindowFrameSection", { enumerable: true, get: function () { return WindowFrameSection_1.WindowFrameSection; } });
var WindowModality_1 = require("./WindowModality");
Object.defineProperty(exports, "WindowModality", { enumerable: true, get: function () { return WindowModality_1.WindowModality; } });
var WindowState_1 = require("./WindowState");
Object.defineProperty(exports, "WindowState", { enumerable: true, get: function () { return WindowState_1.WindowState; } });
var WindowType_1 = require("./WindowType");
Object.defineProperty(exports, "WindowType", { enumerable: true, get: function () { return WindowType_1.WindowType; } });
var PenStyle_1 = require("./PenStyle");
Object.defineProperty(exports, "PenStyle", { enumerable: true, get: function () { return PenStyle_1.PenStyle; } });
var PenCapStyle_1 = require("./PenCapStyle");
Object.defineProperty(exports, "PenCapStyle", { enumerable: true, get: function () { return PenCapStyle_1.PenCapStyle; } });
var DialogCode_1 = require("./DialogCode");
Object.defineProperty(exports, "DialogCode", { enumerable: true, get: function () { return DialogCode_1.DialogCode; } });
var Visibility_1 = require("./Visibility");
Object.defineProperty(exports, "Visibility", { enumerable: true, get: function () { return Visibility_1.Visibility; } });
var StackingMode_1 = require("./StackingMode");
Object.defineProperty(exports, "StackingMode", { enumerable: true, get: function () { return StackingMode_1.StackingMode; } });

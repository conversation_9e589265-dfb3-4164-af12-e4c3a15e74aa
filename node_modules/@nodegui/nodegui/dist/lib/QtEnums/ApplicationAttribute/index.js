"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationAttribute = void 0;
var ApplicationAttribute;
(function (ApplicationAttribute) {
    ApplicationAttribute[ApplicationAttribute["AA_DontShowIconsInMenus"] = 2] = "AA_DontShowIconsInMenus";
    ApplicationAttribute[ApplicationAttribute["AA_DontShowShortcutsInContextMenus"] = 28] = "AA_DontShowShortcutsInContextMenus";
    ApplicationAttribute[ApplicationAttribute["AA_NativeWindows"] = 3] = "AA_NativeWindows";
    ApplicationAttribute[ApplicationAttribute["AA_DontCreateNativeWidgetSiblings"] = 4] = "AA_DontCreateNativeWidgetSiblings";
    ApplicationAttribute[ApplicationAttribute["AA_PluginApplication"] = 5] = "AA_PluginApplication";
    ApplicationAttribute[ApplicationAttribute["AA_DontUseNativeMenuBar"] = 6] = "AA_DontUseNativeMenuBar";
    ApplicationAttribute[ApplicationAttribute["AA_MacDontSwapCtrlAndMeta"] = 7] = "AA_MacDontSwapCtrlAndMeta";
    ApplicationAttribute[ApplicationAttribute["AA_Use96Dpi"] = 8] = "AA_Use96Dpi";
    ApplicationAttribute[ApplicationAttribute["AA_SynthesizeTouchForUnhandledMouseEvents"] = 11] = "AA_SynthesizeTouchForUnhandledMouseEvents";
    ApplicationAttribute[ApplicationAttribute["AA_SynthesizeMouseForUnhandledTouchEvents"] = 12] = "AA_SynthesizeMouseForUnhandledTouchEvents";
    ApplicationAttribute[ApplicationAttribute["AA_UseHighDpiPixmaps"] = 13] = "AA_UseHighDpiPixmaps";
    ApplicationAttribute[ApplicationAttribute["AA_ForceRasterWidgets"] = 14] = "AA_ForceRasterWidgets";
    ApplicationAttribute[ApplicationAttribute["AA_UseDesktopOpenGL"] = 15] = "AA_UseDesktopOpenGL";
    ApplicationAttribute[ApplicationAttribute["AA_UseOpenGLES"] = 16] = "AA_UseOpenGLES";
    ApplicationAttribute[ApplicationAttribute["AA_UseSoftwareOpenGL"] = 17] = "AA_UseSoftwareOpenGL";
    ApplicationAttribute[ApplicationAttribute["AA_ShareOpenGLContexts"] = 18] = "AA_ShareOpenGLContexts";
    ApplicationAttribute[ApplicationAttribute["AA_SetPalette"] = 19] = "AA_SetPalette";
    ApplicationAttribute[ApplicationAttribute["AA_EnableHighDpiScaling"] = 20] = "AA_EnableHighDpiScaling";
    ApplicationAttribute[ApplicationAttribute["AA_DisableHighDpiScaling"] = 21] = "AA_DisableHighDpiScaling";
    ApplicationAttribute[ApplicationAttribute["AA_UseStyleSheetPropagationInWidgetStyles"] = 22] = "AA_UseStyleSheetPropagationInWidgetStyles";
    ApplicationAttribute[ApplicationAttribute["AA_DontUseNativeDialogs"] = 23] = "AA_DontUseNativeDialogs";
    ApplicationAttribute[ApplicationAttribute["AA_SynthesizeMouseForUnhandledTabletEvents"] = 24] = "AA_SynthesizeMouseForUnhandledTabletEvents";
    ApplicationAttribute[ApplicationAttribute["AA_CompressHighFrequencyEvents"] = 25] = "AA_CompressHighFrequencyEvents";
    ApplicationAttribute[ApplicationAttribute["AA_CompressTabletEvents"] = 29] = "AA_CompressTabletEvents";
    ApplicationAttribute[ApplicationAttribute["AA_DontCheckOpenGLContextThreadAffinity"] = 26] = "AA_DontCheckOpenGLContextThreadAffinity";
    ApplicationAttribute[ApplicationAttribute["AA_DisableShaderDiskCache"] = 27] = "AA_DisableShaderDiskCache";
    ApplicationAttribute[ApplicationAttribute["AA_DisableWindowContextHelpButton"] = 30] = "AA_DisableWindowContextHelpButton";
})(ApplicationAttribute = exports.ApplicationAttribute || (exports.ApplicationAttribute = {}));

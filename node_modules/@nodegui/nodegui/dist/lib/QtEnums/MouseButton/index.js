"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouseButton = void 0;
var MouseButton;
(function (MouseButton) {
    MouseButton[MouseButton["NoButton"] = 0] = "NoButton";
    MouseButton[MouseButton["AllButtons"] = 134217727] = "AllButtons";
    MouseButton[MouseButton["LeftButton"] = 1] = "LeftButton";
    MouseButton[MouseButton["RightButton"] = 2] = "RightButton";
    MouseButton[MouseButton["MidButton"] = 4] = "MidButton";
    <PERSON>B<PERSON>on[MouseButton["MiddleButton"] = 4] = "MiddleButton";
    <PERSON>Button[MouseButton["BackButton"] = 8] = "BackButton";
    <PERSON><PERSON><PERSON>on[MouseButton["XButton1"] = 8] = "XButton1";
    Mouse<PERSON>utton[MouseButton["ExtraButton1"] = 8] = "ExtraButton1";
    <PERSON><PERSON>utton[MouseButton["ForwardButton"] = 16] = "ForwardButton";
    MouseButton[MouseButton["XButton2"] = 16] = "XButton2";
    MouseB<PERSON><PERSON>[MouseButton["ExtraButton2"] = 16] = "ExtraButton2";
    MouseButton[MouseButton["TaskButton"] = 32] = "TaskButton";
    MouseButton[MouseButton["ExtraButton3"] = 32] = "ExtraButton3";
    MouseButton[MouseButton["ExtraButton4"] = 64] = "ExtraButton4";
    MouseButton[MouseButton["ExtraButton5"] = 128] = "ExtraButton5";
    MouseButton[MouseButton["ExtraButton6"] = 256] = "ExtraButton6";
    MouseButton[MouseButton["ExtraButton7"] = 512] = "ExtraButton7";
    MouseButton[MouseButton["ExtraButton8"] = 1024] = "ExtraButton8";
    MouseButton[MouseButton["ExtraButton9"] = 2048] = "ExtraButton9";
    MouseButton[MouseButton["ExtraButton10"] = 4096] = "ExtraButton10";
    MouseButton[MouseButton["ExtraButton11"] = 8192] = "ExtraButton11";
    MouseButton[MouseButton["ExtraButton12"] = 16384] = "ExtraButton12";
    MouseButton[MouseButton["ExtraButton13"] = 32768] = "ExtraButton13";
    MouseButton[MouseButton["ExtraButton14"] = 65536] = "ExtraButton14";
    MouseButton[MouseButton["ExtraButton15"] = 131072] = "ExtraButton15";
    MouseButton[MouseButton["ExtraButton16"] = 262144] = "ExtraButton16";
    MouseButton[MouseButton["ExtraButton17"] = 524288] = "ExtraButton17";
    MouseButton[MouseButton["ExtraButton18"] = 1048576] = "ExtraButton18";
    MouseButton[MouseButton["ExtraButton19"] = 2097152] = "ExtraButton19";
    MouseButton[MouseButton["ExtraButton20"] = 4194304] = "ExtraButton20";
    MouseButton[MouseButton["ExtraButton21"] = 8388608] = "ExtraButton21";
    MouseButton[MouseButton["ExtraButton22"] = 16777216] = "ExtraButton22";
    MouseButton[MouseButton["ExtraButton23"] = 33554432] = "ExtraButton23";
    MouseButton[MouseButton["ExtraButton24"] = 67108864] = "ExtraButton24";
})(MouseButton = exports.MouseButton || (exports.MouseButton = {}));

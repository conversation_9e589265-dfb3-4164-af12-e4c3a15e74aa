"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SizeHint = void 0;
var SizeHint;
(function (SizeHint) {
    SizeHint[SizeHint["MinimumSize"] = 0] = "MinimumSize";
    SizeHint[SizeHint["PreferredSize"] = 1] = "PreferredSize";
    SizeHint[SizeHint["MaximumSize"] = 2] = "MaximumSize";
    SizeHint[SizeHint["MinimumDescent"] = 3] = "MinimumDescent";
})(SizeHint = exports.SizeHint || (exports.SizeHint = {}));

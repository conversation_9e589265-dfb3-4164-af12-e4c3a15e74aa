"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolButtonStyle = void 0;
var ToolButtonStyle;
(function (ToolButtonStyle) {
    ToolButtonStyle[ToolButtonStyle["ToolButtonIconOnly"] = 0] = "ToolButtonIconOnly";
    ToolButtonStyle[ToolButtonStyle["ToolButtonTextOnly"] = 1] = "ToolButtonTextOnly";
    ToolButtonStyle[ToolButtonStyle["ToolButtonTextBesideIcon"] = 2] = "ToolButtonTextBesideIcon";
    ToolButtonStyle[ToolButtonStyle["ToolButtonTextUnderIcon"] = 3] = "ToolButtonTextUnderIcon";
    ToolButtonStyle[ToolButtonStyle["ToolButtonFollowStyle"] = 4] = "ToolButtonFollowStyle";
})(ToolButtonStyle = exports.ToolButtonStyle || (exports.ToolButtonStyle = {}));

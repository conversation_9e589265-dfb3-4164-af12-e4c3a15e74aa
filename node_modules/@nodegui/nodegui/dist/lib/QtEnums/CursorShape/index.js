"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CursorShape = void 0;
var CursorShape;
(function (CursorShape) {
    CursorShape[CursorShape["ArrowCursor"] = 0] = "ArrowCursor";
    CursorShape[CursorShape["UpArrowCursor"] = 1] = "UpArrowCursor";
    CursorShape[CursorShape["CrossCursor"] = 2] = "CrossCursor";
    CursorShape[CursorShape["WaitCursor"] = 3] = "WaitCursor";
    CursorShape[CursorShape["IBeamCursor"] = 4] = "IBeamCursor";
    CursorShape[CursorShape["SizeVerCursor"] = 5] = "SizeVerCursor";
    CursorShape[CursorShape["SizeHorCursor"] = 6] = "SizeHorCursor";
    CursorShape[CursorShape["SizeBDiagCursor"] = 7] = "SizeBDiagCursor";
    CursorShape[CursorShape["SizeFDiagCursor"] = 8] = "SizeFDiagCursor";
    CursorShape[CursorShape["SizeAllCursor"] = 9] = "SizeAllCursor";
    CursorShape[CursorShape["BlankCursor"] = 10] = "BlankCursor";
    CursorShape[CursorShape["SplitVCursor"] = 11] = "SplitVCursor";
    CursorShape[CursorShape["SplitHCursor"] = 12] = "SplitHCursor";
    CursorShape[CursorShape["PointingHandCursor"] = 13] = "PointingHandCursor";
    CursorShape[CursorShape["ForbiddenCursor"] = 14] = "ForbiddenCursor";
    CursorShape[CursorShape["OpenHandCursor"] = 17] = "OpenHandCursor";
    CursorShape[CursorShape["ClosedHandCursor"] = 18] = "ClosedHandCursor";
    CursorShape[CursorShape["WhatsThisCursor"] = 15] = "WhatsThisCursor";
    CursorShape[CursorShape["BusyCursor"] = 16] = "BusyCursor";
    CursorShape[CursorShape["DragMoveCursor"] = 20] = "DragMoveCursor";
    CursorShape[CursorShape["DragCopyCursor"] = 19] = "DragCopyCursor";
    CursorShape[CursorShape["DragLinkCursor"] = 21] = "DragLinkCursor";
    CursorShape[CursorShape["BitmapCursor"] = 24] = "BitmapCursor";
})(CursorShape = exports.CursorShape || (exports.CursorShape = {}));

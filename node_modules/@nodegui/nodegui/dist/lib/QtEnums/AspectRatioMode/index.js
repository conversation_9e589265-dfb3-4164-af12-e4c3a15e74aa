"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AspectRatioMode = void 0;
var AspectRatioMode;
(function (AspectRatioMode) {
    AspectRatioMode[AspectRatioMode["IgnoreAspectRatio"] = 0] = "IgnoreAspectRatio";
    AspectRatioMode[AspectRatioMode["KeepAspectRatio"] = 1] = "KeepAspectRatio";
    AspectRatioMode[AspectRatioMode["KeepAspectRatioByExpanding"] = 2] = "KeepAspectRatioByExpanding";
})(AspectRatioMode = exports.AspectRatioMode || (exports.AspectRatioMode = {}));

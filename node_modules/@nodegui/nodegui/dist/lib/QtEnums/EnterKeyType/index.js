"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnterKeyType = void 0;
var EnterKeyType;
(function (EnterKeyType) {
    EnterKeyType[EnterKeyType["EnterKeyDefault"] = 0] = "EnterKeyDefault";
    EnterKeyType[EnterKeyType["EnterKeyReturn"] = 1] = "EnterKeyReturn";
    EnterKeyType[EnterKeyType["EnterKeyDone"] = 2] = "EnterKeyDone";
    EnterKeyType[EnterKeyType["EnterKeyGo"] = 3] = "EnterKeyGo";
    EnterKeyType[EnterKeyType["EnterKeySend"] = 4] = "EnterKeySend";
    EnterKeyType[EnterKeyType["EnterKeySearch"] = 5] = "EnterKeySearch";
    EnterKeyType[EnterKeyType["EnterKeyNext"] = 6] = "EnterKeyNext";
    EnterKeyType[EnterKeyType["EnterKeyPrevious"] = 7] = "EnterKeyPrevious";
})(EnterKeyType = exports.EnterKeyType || (exports.EnterKeyType = {}));

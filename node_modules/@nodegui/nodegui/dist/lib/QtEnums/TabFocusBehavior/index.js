"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TabFocusBehavior = void 0;
var TabFocusBehavior;
(function (TabFocusBehavior) {
    TabFocusBehavior[TabFocusBehavior["NoTabFocus"] = 0] = "NoTabFocus";
    TabFocusBehavior[TabFocusBehavior["TabFocusTextControls"] = 1] = "TabFocusTextControls";
    TabFocusBehavior[TabFocusBehavior["TabFocusListControls"] = 2] = "TabFocusListControls";
    TabFocusBehavior[TabFocusBehavior["TabFocusAllControls"] = 255] = "TabFocusAllControls";
})(TabFocusBehavior = exports.TabFocusBehavior || (exports.TabFocusBehavior = {}));

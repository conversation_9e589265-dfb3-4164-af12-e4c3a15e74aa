"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NativeGestureType = void 0;
var NativeGestureType;
(function (NativeGestureType) {
    NativeGestureType[NativeGestureType["BeginNativeGesture"] = 0] = "BeginNativeGesture";
    NativeGestureType[NativeGestureType["EndNativeGesture"] = 1] = "EndNativeGesture";
    NativeGestureType[NativeGestureType["PanNativeGesture"] = 2] = "PanNativeGesture";
    NativeGestureType[NativeGestureType["ZoomNativeGesture"] = 3] = "ZoomNativeGesture";
    NativeGestureType[NativeGestureType["SmartZoomNativeGesture"] = 4] = "SmartZoomNativeGesture";
    NativeGestureType[NativeGestureType["RotateNativeGesture"] = 5] = "RotateNativeGesture";
    NativeGestureType[NativeGestureType["SwipeNativeGesture"] = 6] = "SwipeNativeGesture";
})(NativeGestureType = exports.NativeGestureType || (exports.NativeGestureType = {}));

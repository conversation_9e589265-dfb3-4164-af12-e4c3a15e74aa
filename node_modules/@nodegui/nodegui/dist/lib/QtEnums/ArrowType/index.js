"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArrowType = void 0;
var ArrowType;
(function (ArrowType) {
    ArrowType[ArrowType["NoArrow"] = 0] = "NoArrow";
    ArrowType[ArrowType["UpArrow"] = 1] = "UpArrow";
    ArrowType[ArrowType["DownArrow"] = 2] = "DownArrow";
    ArrowType[ArrowType["LeftArrow"] = 3] = "LeftArrow";
    ArrowType[ArrowType["RightArrow"] = 4] = "RightArrow";
})(ArrowType = exports.ArrowType || (exports.ArrowType = {}));

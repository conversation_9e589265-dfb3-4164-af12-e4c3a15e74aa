export declare enum WidgetAttribute {
    WA_AcceptDrops = 78,
    WA_AlwaysShowToolTips = 84,
    WA_ContentsPropagated = 3,
    WA_CustomWhatsThis = 47,
    WA_DeleteOnClose = 55,
    WA_Disabled = 0,
    WA_DontShowOnScreen = 103,
    WA_ForceDisabled = 32,
    WA_ForceUpdatesDisabled = 59,
    WA_GroupLeader = 72,
    WA_Hover = 74,
    WA_InputMethodEnabled = 14,
    WA_KeyboardFocusChange = 77,
    WA_KeyCompression = 33,
    WA_LayoutOnEntireRect = 48,
    WA_LayoutUsesWidgetRect = 92,
    WA_MacNoClickThrough = 12,
    WA_MacOpaqueSizeGrip = 85,
    WA_MacShowFocusRect = 88,
    WA_MacNormalSize = 89,
    WA_MacSmallSize = 90,
    WA_MacMiniSize = 91,
    WA_MacVariableSize = 102,
    WA_MacBrushedMetal = 46,
    WA_Mapped = 11,
    WA_MouseNoMask = 71,
    WA_MouseTracking = 2,
    WA_Moved = 43,
    WA_MSWindowsUseDirect3D = 94,
    WA_NoBackground = 4,
    WA_NoChildEventsForParent = 58,
    WA_NoChildEventsFromChildren = 39,
    WA_NoMouseReplay = 54,
    WA_NoMousePropagation = 73,
    WA_TransparentForMouseEvents = 51,
    WA_NoSystemBackground = 9,
    WA_OpaquePaintEvent = 4,
    WA_OutsideWSRange = 49,
    WA_PaintOnScreen = 8,
    WA_PaintUnclipped = 52,
    WA_PendingMoveEvent = 34,
    WA_PendingResizeEvent = 35,
    WA_QuitOnClose = 76,
    WA_Resized = 42,
    WA_RightToLeft = 56,
    WA_SetCursor = 38,
    WA_SetFont = 37,
    WA_SetPalette = 36,
    WA_SetStyle = 86,
    WA_ShowModal = 70,
    WA_StaticContents = 5,
    WA_StyleSheet = 97,
    WA_StyleSheetTarget = 131,
    WA_TabletTracking = 129,
    WA_TranslucentBackground = 120,
    WA_UnderMouse = 1,
    WA_UpdatesDisabled = 10,
    WA_WindowModified = 41,
    WA_WindowPropagation = 80,
    WA_MacAlwaysShowToolWindow = 96,
    WA_SetLocale = 87,
    WA_StyledBackground = 93,
    WA_ShowWithoutActivating = 98,
    WA_NativeWindow = 100,
    WA_DontCreateNativeAncestors = 101,
    WA_X11NetWmWindowTypeDesktop = 104,
    WA_X11NetWmWindowTypeDock = 105,
    WA_X11NetWmWindowTypeToolBar = 106,
    WA_X11NetWmWindowTypeMenu = 107,
    WA_X11NetWmWindowTypeUtility = 108,
    WA_X11NetWmWindowTypeSplash = 109,
    WA_X11NetWmWindowTypeDialog = 110,
    WA_X11NetWmWindowTypeDropDownMenu = 111,
    WA_X11NetWmWindowTypePopupMenu = 112,
    WA_X11NetWmWindowTypeToolTip = 113,
    WA_X11NetWmWindowTypeNotification = 114,
    WA_X11NetWmWindowTypeCombo = 115,
    WA_X11NetWmWindowTypeDND = 116,
    WA_MacFrameworkScaled = 117,
    WA_AcceptTouchEvents = 121,
    WA_TouchPadAcceptSingleTouchEvents = 123,
    WA_X11DoNotAcceptFocus = 126,
    WA_AlwaysStackOnTop = 128,
    WA_ContentsMarginsRespectsSafeArea = 130
}

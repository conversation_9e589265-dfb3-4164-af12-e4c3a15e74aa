"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemDataRole = void 0;
var ItemDataRole;
(function (ItemDataRole) {
    ItemDataRole[ItemDataRole["DisplayRole"] = 0] = "DisplayRole";
    ItemDataRole[ItemDataRole["DecorationRole"] = 1] = "DecorationRole";
    ItemDataRole[ItemDataRole["EditRole"] = 2] = "EditRole";
    ItemDataRole[ItemDataRole["ToolTipRole"] = 3] = "ToolTipRole";
    ItemDataRole[ItemDataRole["StatusTipRole"] = 4] = "StatusTipRole";
    ItemDataRole[ItemDataRole["WhatsThisRole"] = 5] = "WhatsThisRole";
    ItemDataRole[ItemDataRole["FontRole"] = 6] = "FontRole";
    ItemDataRole[ItemDataRole["TextAlignmentRole"] = 7] = "TextAlignmentRole";
    ItemDataRole[ItemDataRole["BackgroundRole"] = 8] = "BackgroundRole";
    ItemDataRole[ItemDataRole["ForegroundRole"] = 9] = "ForegroundRole";
    ItemDataRole[ItemDataRole["CheckStateRole"] = 10] = "CheckStateRole";
    ItemDataRole[ItemDataRole["AccessibleTextRole"] = 11] = "AccessibleTextRole";
    ItemDataRole[ItemDataRole["AccessibleDescriptionRole"] = 12] = "AccessibleDescriptionRole";
    ItemDataRole[ItemDataRole["SizeHintRole"] = 13] = "SizeHintRole";
    ItemDataRole[ItemDataRole["InitialSortOrderRole"] = 14] = "InitialSortOrderRole";
    ItemDataRole[ItemDataRole["UserRole"] = 256] = "UserRole";
})(ItemDataRole = exports.ItemDataRole || (exports.ItemDataRole = {}));

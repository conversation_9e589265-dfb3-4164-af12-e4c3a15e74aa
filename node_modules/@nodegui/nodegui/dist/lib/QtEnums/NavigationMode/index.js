"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NavigationMode = void 0;
var NavigationMode;
(function (NavigationMode) {
    NavigationMode[NavigationMode["NavigationModeNone"] = 0] = "NavigationModeNone";
    NavigationMode[NavigationMode["NavigationModeKeypadTabOrder"] = 1] = "NavigationModeKeypadTabOrder";
    NavigationMode[NavigationMode["NavigationModeKeypadDirectional"] = 2] = "NavigationModeKeypadDirectional";
    NavigationMode[NavigationMode["NavigationModeCursorAuto"] = 3] = "NavigationModeCursorAuto";
    NavigationMode[NavigationMode["NavigationModeCursorForceVisible"] = 4] = "NavigationModeCursorForceVisible";
})(NavigationMode = exports.NavigationMode || (exports.NavigationMode = {}));

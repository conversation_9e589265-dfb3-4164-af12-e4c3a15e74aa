"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WindowType = void 0;
var WindowType;
(function (WindowType) {
    WindowType[WindowType["Widget"] = 0] = "Widget";
    WindowType[WindowType["Window"] = 1] = "Window";
    WindowType[WindowType["Dialog"] = 3] = "Dialog";
    WindowType[WindowType["Sheet"] = 5] = "Sheet";
    WindowType[WindowType["Drawer"] = 8] = "Drawer";
    WindowType[WindowType["Popup"] = 9] = "Popup";
    WindowType[WindowType["Tool"] = 18] = "Tool";
    WindowType[WindowType["ToolTip"] = 20] = "ToolTip";
    WindowType[WindowType["SplashScreen"] = 23] = "SplashScreen";
    WindowType[WindowType["Desktop"] = 17] = "Desktop";
    WindowType[WindowType["SubWindow"] = 18] = "SubWindow";
    WindowType[WindowType["ForeignWindow"] = 33] = "ForeignWindow";
    WindowType[WindowType["CoverWindow"] = 65] = "CoverWindow";
    //There are also a number of flags which you can use to customize the appearance of top-level windows. These have no effect on other windows
    WindowType[WindowType["MSWindowsFixedSizeDialogHint"] = 256] = "MSWindowsFixedSizeDialogHint";
    WindowType[WindowType["MSWindowsOwnDC"] = 512] = "MSWindowsOwnDC";
    WindowType[WindowType["BypassWindowManagerHint"] = 1024] = "BypassWindowManagerHint";
    WindowType[WindowType["X11BypassWindowManagerHint"] = 1024] = "X11BypassWindowManagerHint";
    WindowType[WindowType["FramelessWindowHint"] = 2048] = "FramelessWindowHint";
    WindowType[WindowType["NoDropShadowWindowHint"] = 1073741824] = "NoDropShadowWindowHint";
    WindowType[WindowType["CustomizeWindowHint"] = 33554432] = "CustomizeWindowHint";
    WindowType[WindowType["WindowTitleHint"] = 4096] = "WindowTitleHint";
    WindowType[WindowType["WindowSystemMenuHint"] = 8192] = "WindowSystemMenuHint";
    WindowType[WindowType["WindowMinimizeButtonHint"] = 16384] = "WindowMinimizeButtonHint";
    WindowType[WindowType["WindowMaximizeButtonHint"] = 32768] = "WindowMaximizeButtonHint";
    WindowType[WindowType["WindowMinMaxButtonsHint"] = 73728] = "WindowMinMaxButtonsHint";
    WindowType[WindowType["WindowCloseButtonHint"] = 134217728] = "WindowCloseButtonHint";
    WindowType[WindowType["WindowContextHelpButtonHint"] = 65536] = "WindowContextHelpButtonHint";
    WindowType[WindowType["MacWindowToolBarButtonHint"] = 268435456] = "MacWindowToolBarButtonHint";
    WindowType[WindowType["WindowFullscreenButtonHint"] = 2147483648] = "WindowFullscreenButtonHint";
    WindowType[WindowType["BypassGraphicsProxyWidget"] = 536870912] = "BypassGraphicsProxyWidget";
    WindowType[WindowType["WindowShadeButtonHint"] = 131072] = "WindowShadeButtonHint";
    WindowType[WindowType["WindowStaysOnTopHint"] = 262144] = "WindowStaysOnTopHint";
    WindowType[WindowType["WindowStaysOnBottomHint"] = 67108864] = "WindowStaysOnBottomHint";
    WindowType[WindowType["WindowTransparentForInput"] = 524288] = "WindowTransparentForInput";
    WindowType[WindowType["WindowOverridesSystemGestures"] = 1048576] = "WindowOverridesSystemGestures";
    WindowType[WindowType["WindowDoesNotAcceptFocus"] = 2097152] = "WindowDoesNotAcceptFocus";
    WindowType[WindowType["MaximizeUsingFullscreenGeometryHint"] = 4194304] = "MaximizeUsingFullscreenGeometryHint";
    WindowType[WindowType["WindowType_Mask"] = 255] = "WindowType_Mask";
})(WindowType = exports.WindowType || (exports.WindowType = {}));

export declare enum WindowType {
    Widget = 0,
    Window = 1,
    Dialog = 3,
    Sheet = 5,
    Drawer = 8,
    Popup = 9,
    Tool = 18,
    ToolTip = 20,
    SplashScreen = 23,
    Desktop = 17,
    SubWindow = 18,
    ForeignWindow = 33,
    CoverWindow = 65,
    MSWindowsFixedSizeDialogHint = 256,
    MSWindowsOwnDC = 512,
    BypassWindowManagerHint = 1024,
    X11BypassWindowManagerHint = 1024,
    FramelessWindowHint = 2048,
    NoDropShadowWindowHint = 1073741824,
    CustomizeWindowHint = 33554432,
    WindowTitleHint = 4096,
    WindowSystemMenuHint = 8192,
    WindowMinimizeButtonHint = 16384,
    WindowMaximizeButtonHint = 32768,
    WindowMinMaxButtonsHint = 73728,
    WindowCloseButtonHint = 134217728,
    WindowContextHelpButtonHint = 65536,
    MacWindowToolBarButtonHint = 268435456,
    WindowFullscreenButtonHint = 2147483648,
    BypassGraphicsProxyWidget = 536870912,
    WindowShadeButtonHint = 131072,
    WindowStaysOnTopHint = 262144,
    WindowStaysOnBottomHint = 67108864,
    WindowTransparentForInput = 524288,
    WindowOverridesSystemGestures = 1048576,
    WindowDoesNotAcceptFocus = 2097152,
    MaximizeUsingFullscreenGeometryHint = 4194304,
    WindowType_Mask = 255
}

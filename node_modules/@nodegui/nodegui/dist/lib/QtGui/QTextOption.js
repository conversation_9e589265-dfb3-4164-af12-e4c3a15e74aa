"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QTextOptionWrapMode = void 0;
var QTextOptionWrapMode;
(function (QTextOptionWrapMode) {
    QTextOptionWrapMode[QTextOptionWrapMode["NoWrap"] = 0] = "NoWrap";
    QTextOptionWrapMode[QTextOptionWrapMode["WordWrap"] = 1] = "WordWrap";
    QTextOptionWrapMode[QTextOptionWrapMode["ManualWrap"] = 2] = "ManualWrap";
    QTextOptionWrapMode[QTextOptionWrapMode["WrapAnywhere"] = 3] = "WrapAnywhere";
    QTextOptionWrapMode[QTextOptionWrapMode["WrapAtWordBoundaryOrAnywhere"] = 4] = "WrapAtWordBoundaryOrAnywhere";
})(QTextOptionWrapMode = exports.QTextOptionWrapMode || (exports.QTextOptionWrapMode = {}));

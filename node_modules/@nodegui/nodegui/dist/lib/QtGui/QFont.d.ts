import { Component, NativeElement } from '../core/Component';
import { QVariant } from '../QtCore/QVariant';
export declare class QFont extends Component {
    constructor();
    constructor(font: QFont);
    constructor(native: NativeElement);
    constructor(family: string, pointSize?: number, weight?: QFontWeight, italic?: boolean);
    bold(): boolean;
    setBold(enable: boolean): void;
    setCapitalization(caps: QFontCapitalization): void;
    capitalization(): QFontCapitalization;
    setFamily(family: string): void;
    family(): string;
    setPixelSize(value: number): void;
    setPointSize(value: number): void;
    pixelSize(): number;
    pointSize(): number;
    setStretch(factor: QFontStretch): void;
    stretch(): QFontStretch;
    setWeight(weight: QFontWeight): void;
    weight(): QFontWeight;
    setItalic(enable: boolean): void;
    italic(): boolean;
    setStyleName(style: string): void;
    toString(): string;
    static fromQVariant(variant: QVariant): QFont;
}
export declare enum QFontStretch {
    AnyStretch = 0,
    UltraCondensed = 50,
    ExtraCondensed = 62,
    Condensed = 75,
    SemiCondensed = 87,
    Unstretched = 100,
    SemiExpanded = 112,
    Expanded = 125,
    ExtraExpanded = 150,
    UltraExpanded = 200
}
export declare enum QFontCapitalization {
    MixedCase = 0,
    AllUppercase = 1,
    AllLowercase = 2,
    SmallCaps = 3,
    Capitalize = 4
}
export declare enum QFontWeight {
    Thin = 100,
    ExtraLight = 200,
    Light = 300,
    Normal = 400,
    Medium = 500,
    DemiBold = 600,
    Bold = 700,
    ExtraBold = 800,
    Black = 900
}

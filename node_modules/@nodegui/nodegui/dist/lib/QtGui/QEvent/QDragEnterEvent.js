"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QDragEnterEvent = void 0;
const addon_1 = __importDefault(require("../../utils/addon"));
const QDragMoveEvent_1 = require("./QDragMoveEvent");
class QDragEnterEvent extends QDragMoveEvent_1.QDragMoveEvent {
    constructor(event) {
        super(new addon_1.default.QDragEnterEvent(event));
    }
}
exports.QDragEnterEvent = QDragEnterEvent;

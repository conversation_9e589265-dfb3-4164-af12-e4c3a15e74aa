import { NativeRawPointer } from '../../core/Component';
import { QVariant, QVariantType } from '../../QtCore/QVariant';
import { QEvent } from './QEvent';
/**
 * Note: Qt performs some default processing for `QInputMethodQueryEvents`.
 * When attaching an event listener via `addEventListener()` use the
 * options object to specify that you want to run after the default
 * processing, otherwise your `setValue()` calls will be overwritten.
 */
export declare class QInputMethodQueryEvent extends QEvent {
    constructor(event: NativeRawPointer<'QEvent'>);
    queries(): number;
    setValue(query: number, value: QVariantType): void;
    value(query: number): QVariant;
}

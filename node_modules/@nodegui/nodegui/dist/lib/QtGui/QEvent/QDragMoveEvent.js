"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QDragMoveEvent = void 0;
const addon_1 = __importDefault(require("../../utils/addon"));
const QDropEvent_1 = require("./QDropEvent");
const helpers_1 = require("../../utils/helpers");
class QDragMoveEvent extends QDropEvent_1.QDropEvent {
    constructor(arg) {
        let native;
        if ((0, helpers_1.checkIfNativeElement)(arg)) {
            native = arg;
        }
        else {
            native = new addon_1.default.QDragMoveEvent(arg);
        }
        super(native);
    }
    /**
     * Sets the accept flag of the event object, the equivalent of calling setAccepted(true).
     * Setting the accept parameter indicates that the event receiver wants the event. Unwanted events might be propagated to the parent widget
     *
     * If the rectangle is provided, also notifies that future moves will also be acceptable if they remain within the rectangle given on the widget.
     * This can improve performance, but may also be ignored by the underlying system.
     * If the rectangle is empty, drag move events will be sent continuously. This is useful if the source is scrolling in a timer event.
     */
    accept(x, y, width, height) {
        if (arguments.length == 4) {
            this.native.accept_qrect(x, y, width, height);
        }
        else {
            this.native.accept();
        }
    }
    /**
     * Clears the accept flag parameter of the event object, the equivalent of calling setAccepted(false).
     * Clearing the accept parameter indicates that the event receiver does not want the event.
     * Unwanted events might be propagated to the parent widget.
     */
    ignore(x, y, width, height) {
        if (arguments.length == 4) {
            this.native.ignore_qrect(x, y, width, height);
        }
        else {
            this.native.ignore();
        }
    }
    answerRect() {
        return this.native.answerRect();
    }
}
exports.QDragMoveEvent = QDragMoveEvent;

import { NativeRawPointer } from '../../core/Component';
import { QEvent } from './QEvent';
export declare class QInputMethodEvent extends QEvent {
    constructor(event: NativeRawPointer<'QEvent'>);
    commitString(): string;
    preeditString(): string;
    replacementLength(): number;
    replacementStart(): number;
    setCommitString(commitString: string, replaceFrom?: number, replaceLength?: number): void;
}

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QInputMethodEvent = void 0;
const addon_1 = __importDefault(require("../../utils/addon"));
const QEvent_1 = require("./QEvent");
class QInputMethodEvent extends QEvent_1.QEvent {
    constructor(event) {
        super(new addon_1.default.QInputMethodEvent(event));
    }
    commitString() {
        return this.native.commitString();
    }
    preeditString() {
        return this.native.preeditString();
    }
    replacementLength() {
        return this.native.replacementLength();
    }
    replacementStart() {
        return this.native.replacementStart();
    }
    setCommitString(commitString, replaceFrom = 0, replaceLength = 0) {
        this.native.setCommitString(commitString, replaceFrom, replaceLength);
    }
}
exports.QInputMethodEvent = QInputMethodEvent;

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QInputMethodQueryEvent = void 0;
const addon_1 = __importDefault(require("../../utils/addon"));
const QVariant_1 = require("../../QtCore/QVariant");
const QEvent_1 = require("./QEvent");
/**
 * Note: Qt performs some default processing for `QInputMethodQueryEvents`.
 * When attaching an event listener via `addEventListener()` use the
 * options object to specify that you want to run after the default
 * processing, otherwise your `setValue()` calls will be overwritten.
 */
class QInputMethodQueryEvent extends QEvent_1.QEvent {
    constructor(event) {
        super(new addon_1.default.QInputMethodQueryEvent(event));
    }
    queries() {
        return this.native.queries();
    }
    setValue(query /* InputMethodQuery */, value) {
        this.native.setValue(query, (0, QVariant_1.nativeObjectFromVariantType)(value));
    }
    value(query /* InputMethodQuery */) {
        return new QVariant_1.QVariant(this.native.value(query));
    }
}
exports.QInputMethodQueryEvent = QInputMethodQueryEvent;

import { Component, NativeElement } from '../core/Component';
import { QVariant } from '../QtCore/QVariant';
import { GlobalColor } from '../QtEnums';
export declare class QColor extends Component {
    constructor();
    constructor(nativeElement: NativeElement);
    constructor(colorString: string);
    constructor(color: GlobalColor);
    constructor(r?: number, g?: number, b?: number, a?: number);
    setRed(value: number): void;
    red(): number;
    setGreen(value: number): void;
    green(): number;
    setBlue(value: number): void;
    blue(): number;
    setAlpha(value: number): void;
    alpha(): number;
    /**
     * Returns the color as a number in ARGB32 format.
     */
    rgb(): number;
    /**
     * Returns the color as a number in ARGB32 format.
     */
    rgba(): number;
    static fromCmyk(c: number, m: number, y: number, k: number, a?: number): QColor;
    static fromCmykF(c: number, m: number, y: number, k: number, a?: number): QColor;
    static fromHsl(h: number, s: number, l: number, a?: number): QColor;
    static fromHslF(h: number, s: number, l: number, a?: number): QColor;
    static fromHsv(h: number, s: number, v: number, a?: number): QColor;
    static fromHsvF(h: number, s: number, v: number, a?: number): QColor;
    static fromRgb(r: number, g: number, b: number, a?: number): QColor;
    static fromRgbF(r: number, g: number, b: number, a?: number): QColor;
    static fromQVariant(variant: QVariant): QColor;
}

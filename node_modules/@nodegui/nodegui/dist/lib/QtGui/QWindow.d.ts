import { NativeElement } from '../core/Component';
import { QObject, QObjectSignals } from '../QtCore/QObject';
import { QScreen } from './QScreen';
import { Edge, Visibility, WindowState } from '../QtEnums';
export declare class QWindow extends QObject<QWindowSignals> {
    constructor(native: NativeElement);
    screen(): QScreen;
    setWindowState(state: WindowState): void;
    showFullScreen(): void;
    showMaximized(): void;
    showMinimized(): void;
    showNormal(): void;
    startSystemMove(): boolean;
    startSystemResize(edges: Edge): boolean;
    windowState(): WindowState;
    visibility(): Visibility;
    setVisibility(visibility: Visibility): void;
}
export interface QWindowSignals extends QObjectSignals {
    screenChanged: (screen: QScreen) => void;
    visibilityChanged: (visibility: Visibility) => void;
    windowStateChanged: (windowState: WindowState) => void;
}

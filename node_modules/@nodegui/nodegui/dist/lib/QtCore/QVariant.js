"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.nativeObjectFromVariantType = exports.QVariant = void 0;
const Component_1 = require("../core/Component");
const addon_1 = __importDefault(require("../utils/addon"));
const helpers_1 = require("../utils/helpers");
const QRect_1 = require("./QRect");
class QVariant extends Component_1.Component {
    constructor(arg) {
        let native;
        if ((0, helpers_1.checkIfNativeElement)(arg) && arg instanceof addon_1.default.QVariant) {
            native = arg;
        }
        else if (arg) {
            native = new addon_1.default.QVariant.convertToQVariant(arg);
        }
        else {
            native = new addon_1.default.QVariant();
        }
        super(native);
    }
    toString() {
        return this.native.toString();
    }
    toInt() {
        return this.native.toInt();
    }
    toDouble() {
        return this.native.toDouble();
    }
    toBool() {
        return this.native.toBool();
    }
    toStringList() {
        return this.native.toStringList();
    }
}
exports.QVariant = QVariant;
/**
 * Get the correct native object which should be passed down to the
 * C++ wrapper from a QVariantType object.
 */
function nativeObjectFromVariantType(obj) {
    if (obj instanceof QRect_1.QRect) {
        return obj.native;
    }
    return obj;
}
exports.nativeObjectFromVariantType = nativeObjectFromVariantType;

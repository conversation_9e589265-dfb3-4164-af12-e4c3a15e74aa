import { NativeElement, Component } from '../core/Component';
import { QRect } from './QRect';
export type QVariantType = NativeElement | string | string[] | number | boolean | QRect;
export declare class QVariant extends Component {
    constructor();
    constructor(nativeElement: NativeElement);
    constructor(variant: QVariantType);
    toString(): string;
    toInt(): number;
    toDouble(): number;
    toBool(): boolean;
    toStringList(): string[];
}
/**
 * Get the correct native object which should be passed down to the
 * C++ wrapper from a QVariantType object.
 */
export declare function nativeObjectFromVariantType(obj: QVariantType): any;

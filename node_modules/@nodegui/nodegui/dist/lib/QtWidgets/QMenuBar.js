"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QMenuBar = void 0;
const QMenu_1 = require("./QMenu");
const QWidget_1 = require("./QWidget");
const addon_1 = __importDefault(require("../utils/addon"));
const helpers_1 = require("../utils/helpers");
const WrapperCache_1 = require("../core/WrapperCache");
const QRect_1 = require("../QtCore/QRect");
const Corner_1 = require("../QtEnums/Corner");
/**

> The QMenuBar class provides a menu widget for use in menu bars, context menus, and other popup menus.

* **This class is a JS wrapper around Qt's [QMenuBar class](https://doc.qt.io/qt-5/qmenu.html)**

### Example

```javascript
const { QMenuBar, QMainWindow } = require("@nodegui/nodegui");
const win = new QMainWindow();
const menu = new QMenuBar();
const menuBar = new QMenuBar();
win.setMenuBar(menuBar);
win.show();
global.win = win;
```
 */
class QMenuBar extends QWidget_1.QWidget {
    constructor(arg) {
        let native;
        if ((0, helpers_1.checkIfNativeElement)(arg)) {
            native = arg;
        }
        else if (arg != null) {
            const parent = arg;
            native = new addon_1.default.QMenuBar(parent.native);
        }
        else {
            native = new addon_1.default.QMenuBar();
        }
        super(native);
    }
    addSeparator() {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.addSeparator());
    }
    actionAt(pt) {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.actionAt(pt.native));
    }
    actionGeometry(act) {
        return new QRect_1.QRect(this.native.actionGeometry(act.native));
    }
    activeAction() {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.activeAction());
    }
    addMenu(menuOrStringOrIcon, title) {
        if (typeof menuOrStringOrIcon === 'string') {
            return WrapperCache_1.wrapperCache.getWrapper(this.native.addMenu_1(menuOrStringOrIcon));
        }
        if (menuOrStringOrIcon instanceof QMenu_1.QMenu) {
            return WrapperCache_1.wrapperCache.getWrapper(this.native.addMenu(menuOrStringOrIcon.native));
        }
        return WrapperCache_1.wrapperCache.getWrapper(this.native.addMenu_2(menuOrStringOrIcon.native, title));
    }
    clear() {
        this.native.clear();
    }
    cornerWidget(corner = Corner_1.Corner.TopRightCorner) {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.cornerWidget(corner));
    }
    insertMenu(before, menu) {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.insertMenu(before.native, menu.native));
    }
    insertSeparator(before) {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.insertSeparator(before.native));
    }
    setActiveAction(act) {
        this.native.setActiveAction(act.native);
    }
    setCornerWidget(widget, corner = Corner_1.Corner.TopRightCorner) {
        this.native.setCornerWidget(widget, corner);
    }
    // CLASS: QMenuBar
    // TODO: NSMenu *	toNSMenu()
    // CLASS: QMenuBar
    isNativeMenuBar() {
        return this.property('isNativeMenuBar').toBool();
    }
    setNativeMenuBar(nativeMenuBar) {
        this.setProperty('nativeMenuBar', nativeMenuBar);
    }
    isDefaultUp() {
        return this.property('isDefaultUp').toBool();
    }
    setDefaultUp(isDefaultUp) {
        this.setProperty('defaultUp', isDefaultUp);
    }
}
exports.QMenuBar = QMenuBar;
WrapperCache_1.wrapperCache.registerWrapper('QMenuBarWrap', QMenuBar);

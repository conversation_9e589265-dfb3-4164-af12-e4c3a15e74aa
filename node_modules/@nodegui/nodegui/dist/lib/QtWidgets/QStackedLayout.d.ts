import { QWidget, QWidgetSignals } from './QWidget';
import { QLayout, QLayoutSignals } from './QLayout';
import { NativeElement } from '../core/Component';
import { StackingMode } from '../QtEnums/StackingMode';
/**

> The QStackedLayout class provides a stack of widgets where only one widget is visible at a time

* **This class is a JS wrapper around Qt's [QStackedLayout](https://doc.qt.io/qt-5/qstackedlayout.html)**

### Example

```javascript
const { QStackedLayout, QWidget, QLabel, QBoxLayout, QCombobox } = require("@nodegui/nodegui");

const centralWidget = new QWidget();
centralWidget.setObjectName('myroot');
const rootLayout = new QBoxLayout(Direction.TopToBottom);
centralWidget.setLayout(rootLayout);

const stackedLayout = new QStackedLayout()

const page1 = new QWidget();
const page1_layout = new FlexLayout();
page1.setLayout(page1_layout)
const label1 = new QLabel();
label1.setText("This is page 1")
page1_layout.addWidget(label1);

const page2 = new QWidget();
const page2_layout = new FlexLayout();
page2.setLayout(page2_layout)
const label2 = new QLabel();
label2.setText("This is page 2")
page2_layout.addWidget(label2);

const page3 = new QWidget();
const page3_layout = new FlexLayout();
page3.setLayout(page3_layout)
const label3 = new QLabel();
label3.setText("This is page 3")
page3_layout.addWidget(label3);

stackedLayout.addWidget(page1)
stackedLayout.addWidget(page2)
stackedLayout.addWidget(page3)

const combobox = new QComboBox()
combobox.addItems(["Page 1", "Page 2", "Page 3"])

combobox.addEventListener("currentIndexChanged", (index) => stackedLayout.setCurrentIndex(index));

rootLayout.addWidget(combobox);

const currentIndexLabel = new QLabel()
currentIndexLabel.setText(`Current Index: ${stackedLayout.currentIndex()}`)

stackedLayout.addEventListener("currentChanged", (index) => {
  currentIndexLabel.setText(`Current Index: ${index}`)
});

rootLayout.addWidget(currentIndexLabel);

rootLayout.addLayout(stackedLayout);

*/
export declare class QStackedLayout extends QLayout<QStackedLayoutSignals> {
    constructor(arg?: QWidget<QWidgetSignals> | NativeElement);
    addWidget(widget: QWidget): void;
    removeWidget(widget: QWidget): void;
    setCurrentIndex(index: number): void;
    insertWidget(index: number, widget: QWidget): void;
    setCurrentWidget(widget: QWidget): void;
    currentIndex(): number;
    currentWidget(): QWidget;
    widget(index: number): QWidget;
    count(): number;
    indexOf(widget: QWidget): number;
    setStackingMode(stackingMode: StackingMode): void;
    stackingMode(): StackingMode;
}
export interface QStackedLayoutSignals extends QLayoutSignals {
    currentChanged: (index: number) => void;
    widgetRemoved: (index: number) => void;
}

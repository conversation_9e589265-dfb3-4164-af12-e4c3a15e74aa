"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QTabWidget = void 0;
const addon_1 = __importDefault(require("../utils/addon"));
const QWidget_1 = require("./QWidget");
const QIcon_1 = require("../QtGui/QIcon");
const WrapperCache_1 = require("../core/WrapperCache");
const helpers_1 = require("../utils/helpers");
/**

> Create and control a stack of tabbed widgets.

* **This class is a JS wrapper around Qt's [QTabWidget class](https://doc.qt.io/qt-5/qtabwidget.html)**

A 'QTabWidget' provides a tab bar and a "page area" that is used to display pages related to each tab.

### Example

```javascript
// This example creates two tabs, each holding a separate calendar.
const { QTabWidget, QCalendarWidget, QIcon } = require("@nodegui/nodegui");

const tabWidget = new QTabWidget();

tabWidget.addTab(new QCalendarWidget(), new QIcon(), 'Tab 1');
tabWidget.addTab(new QCalendarWidget(), new QIcon(), 'Tab 2');
```
 */
class QTabWidget extends QWidget_1.QWidget {
    constructor(arg) {
        let native;
        if ((0, helpers_1.checkIfNativeElement)(arg)) {
            native = arg;
        }
        else if (arg != null) {
            const parent = arg;
            native = new addon_1.default.QTabWidget(parent.native);
        }
        else {
            native = new addon_1.default.QTabWidget();
        }
        super(native);
    }
    /**
     * Adds a tab to the TabBar of the widget.
     * If you include an ampersand (`&`) in the label, the next character will become the shortcut to this tab.
     * Eg: the label "Bro&wse" will assign ALT+W to focus on this tab.
     * @param page The widget that will become the body of the tab.
     * @param iconOrLabel The icon of the tab (optional). When you omit the icon, this must be the label.
     * @param label The label of the tab.
     * @returns The index of the tab.
     */
    addTab(page, iconOrLabel, label) {
        let index;
        if (iconOrLabel instanceof String) {
            index = this.native.addTab_2(page.native, iconOrLabel);
        }
        else if (iconOrLabel instanceof QIcon_1.QIcon && label !== undefined) {
            index = this.native.addTab_3(page.native, iconOrLabel.native, label);
        }
        page.setFlexNodeSizeControlled(true);
        return index;
    }
    /**
     * Adds a tab to the TabBar of the widget to a specific position.
     * If you include an ampersand (`&`) in the label, the next character will become the shortcut to this tab.
     * Eg: the label "Bro&wse" will assign ALT+W to focus on this tab.
     * @param index The index where the tab will be inserted.
     * @param page The widget that will become the body of the Tab.
     * @param iconOrLabel The icon of the tab (optional). When you omit the icon, this must be the label.
     * @param label The label of the tab.
     * @returns The new index of the tab
     */
    insertTab(index, page, iconOrLabel, label) {
        let newIndex;
        if (iconOrLabel instanceof String) {
            newIndex = this.native.insertTab_3(index, page.native, iconOrLabel);
        }
        else if (iconOrLabel instanceof QIcon_1.QIcon && label !== undefined) {
            newIndex = this.native.insertTab_4(index, page.native, iconOrLabel.native, label);
        }
        return newIndex;
    }
    indexOf(widget) {
        return this.native.indexOf(widget.native);
    }
    setTabPosition(tabPosition) {
        this.native.setTabPosition(tabPosition);
    }
    setTabText(tabIndex, tabText) {
        this.native.setTabText(tabIndex, tabText);
    }
    setTabIcon(tabIndex, icon) {
        this.native.setTabIcon(tabIndex, icon.native);
    }
    setCurrentIndex(index) {
        this.native.setCurrentIndex(index);
    }
    currentIndex() {
        return this.native.currentIndex();
    }
    removeTab(index) {
        this.native.removeTab(index);
        const toRemove = this.widget(index);
        toRemove.setFlexNodeSizeControlled(false);
    }
    setTabsClosable(closeable) {
        this.native.setTabsClosable(closeable);
    }
    widget(index) {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.widget(index));
    }
}
exports.QTabWidget = QTabWidget;
WrapperCache_1.wrapperCache.registerWrapper('QTabWidgetWrap', QTabWidget);

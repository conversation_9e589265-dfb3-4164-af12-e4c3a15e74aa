"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QMenu = void 0;
const QWidget_1 = require("./QWidget");
const addon_1 = __importDefault(require("../utils/addon"));
const WrapperCache_1 = require("../core/WrapperCache");
const helpers_1 = require("../utils/helpers");
const QRect_1 = require("../QtCore/QRect");
/**

> The QMenu class provides a menu widget for use in menu bars, context menus, and other popup menus.

* **This class is a JS wrapper around Qt's [QMenu class](https://doc.qt.io/qt-5/qmenu.html)**

### Example

```javascript
const { QMenu } = require("@nodegui/nodegui");

const menu = new QMenu();
```
 */
class QMenu extends QWidget_1.QWidget {
    constructor(arg) {
        let native;
        if ((0, helpers_1.checkIfNativeElement)(arg)) {
            native = arg;
        }
        else if (arg != null) {
            const parent = arg;
            native = new addon_1.default.QMenu(parent.native);
        }
        else {
            native = new addon_1.default.QMenu();
        }
        super(native);
    }
    clear() {
        this.native.clear();
    }
    addSeparator() {
        return this.native.addSeparator();
    }
    exec(point, action) {
        if (point && action) {
            this.native.exec(point.native, action.native);
        }
        else if (point) {
            this.native.exec(point.native);
        }
        else {
            this.native.exec();
        }
    }
    popup(point, action) {
        this.native.popup(point.native, action?.native);
    }
    // CLASS: QMenu
    // TODO: QAction *	addMenu(QMenu *menu)
    // TODO: NSMenu *	toNSMenu()
    actionAt(pt) {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.actionAt(pt.native));
    }
    actionGeometry(act) {
        return new QRect_1.QRect(this.native.actionGeometry(act.native));
    }
    activeAction() {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.activeAction());
    }
    addMenu(titleOrIcon, title) {
        if (typeof titleOrIcon === 'string') {
            return WrapperCache_1.wrapperCache.getWrapper(this.native.addMenu_1(title));
        }
        else {
            return WrapperCache_1.wrapperCache.getWrapper(this.native.addMenu_2(titleOrIcon.native, title));
        }
    }
    addSection(textOrIcon, text) {
        if (typeof textOrIcon === 'string') {
            return WrapperCache_1.wrapperCache.getWrapper(this.native.addSection_1(textOrIcon));
        }
        else {
            return WrapperCache_1.wrapperCache.getWrapper(this.native.addSection_2(textOrIcon.native, text));
        }
    }
    defaultAction() {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.defaultAction());
    }
    hideTearOffMenu() {
        this.native.hideTearOffMenu();
    }
    insertMenu(before, menu) {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.insertMenu(before.native, menu.native));
    }
    insertSection(before, textOrIcon, text) {
        if (typeof textOrIcon === 'string') {
            return WrapperCache_1.wrapperCache.getWrapper(this.native.insertSection_2(before.native, text));
        }
        else {
            return WrapperCache_1.wrapperCache.getWrapper(this.native.insertSection_3(before.native, textOrIcon.native, text));
        }
    }
    insertSeparator(before) {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.insertSeparator(before.native));
    }
    isEmpty() {
        return this.native.isEmpty();
    }
    isTearOffMenuVisible() {
        return this.native.isTearOffMenuVisible();
    }
    menuAction() {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.menuAction());
    }
    setActiveAction(act) {
        this.native.setActiveAction(act.native);
    }
    setAsDockMenu() {
        this.native.setAsDockMenu();
    }
    setDefaultAction(act) {
        this.native.setDefaultAction(act.native);
    }
    showTearOffMenu(pos = null) {
        if (pos == null) {
            this.native.showTearOffMenu_0();
        }
        else {
            this.native.showTearOffMenu_1(pos.native);
        }
    }
    icon() {
        return this.native.icon();
    }
    setIcon(icon) {
        this.native.setIcon(icon);
    }
    separatorsCollapsible() {
        return this.property('separatorsCollapsible').toBool();
    }
    setSeparatorsCollapsible(collapse) {
        this.setProperty('separatorsCollapsible', collapse);
    }
    isTearOffEnabled() {
        return this.property('isTearOffEnabled').toBool();
    }
    setTearOffEnabled(tearOffEnabled) {
        this.setProperty('tearOffEnabled', tearOffEnabled);
    }
    toolTipsVisible() {
        return this.property('toolTipsVisible').toBool();
    }
    setToolTipsVisible(visible) {
        this.setProperty('toolTipsVisible', visible);
    }
    title() {
        return this.property('title').toString();
    }
    setTitle(title) {
        this.setProperty('title', title);
    }
}
exports.QMenu = QMenu;
WrapperCache_1.wrapperCache.registerWrapper('QMenuWrap', QMenu);

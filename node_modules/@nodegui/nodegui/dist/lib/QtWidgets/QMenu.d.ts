import { NativeElement } from '../core/Component';
import { QWidget, QWidgetSignals } from './QWidget';
import { QAction } from './QAction';
import { QPoint } from '../QtCore/QPoint';
import { QRect } from '../QtCore/QRect';
import { QIcon } from '../QtGui/QIcon';
/**

> The QMenu class provides a menu widget for use in menu bars, context menus, and other popup menus.

* **This class is a JS wrapper around Qt's [QMenu class](https://doc.qt.io/qt-5/qmenu.html)**

### Example

```javascript
const { QMenu } = require("@nodegui/nodegui");

const menu = new QMenu();
```
 */
export declare class QMenu extends QWidget<QMenuSignals> {
    constructor(arg?: QWidget<QWidgetSignals> | NativeElement);
    clear(): void;
    addSeparator(): QAction;
    exec(point?: QPoint, action?: QAction | null): void;
    popup(point: QPoint, action?: QAction): void;
    actionAt(pt: QPoint): QAction;
    actionGeometry(act: QAction): QRect;
    activeAction(): QAction;
    addMenu(title: string): QMenu;
    addMenu(icon: QIcon, title: string): QMenu;
    addSection(text: string): QAction;
    addSection(icon: QIcon, text: string): QAction;
    defaultAction(): QAction;
    hideTearOffMenu(): void;
    insertMenu(before: QAction, menu: QMenu): QAction;
    insertSection(before: QAction, text: string): QAction;
    insertSection(before: QAction, icon: QIcon, text: string): QAction;
    insertSeparator(before: QAction): QAction;
    isEmpty(): boolean;
    isTearOffMenuVisible(): boolean;
    menuAction(): QAction;
    setActiveAction(act: QAction): void;
    setAsDockMenu(): void;
    setDefaultAction(act: QAction): void;
    showTearOffMenu(pos?: QPoint): void;
    icon(): QIcon;
    setIcon(icon: QIcon): void;
    separatorsCollapsible(): boolean;
    setSeparatorsCollapsible(collapse: boolean): void;
    isTearOffEnabled(): boolean;
    setTearOffEnabled(tearOffEnabled: boolean): void;
    toolTipsVisible(): boolean;
    setToolTipsVisible(visible: boolean): void;
    title(): string;
    setTitle(title: string): void;
}
export interface QMenuSignals extends QWidgetSignals {
    aboutToHide: () => void;
    aboutToShow: () => void;
    hovered: (action: NativeElement) => void;
    triggered: (action: NativeElement) => void;
}

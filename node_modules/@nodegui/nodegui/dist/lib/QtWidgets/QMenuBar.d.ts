import { QMenu } from './QMenu';
import { NativeElement } from '../core/Component';
import { QWidget, QWidgetSignals } from './QWidget';
import { QAction } from './QAction';
import { QPoint } from '../QtCore/QPoint';
import { QRect } from '../QtCore/QRect';
import { Corner } from '../QtEnums/Corner';
import { QIcon } from '../QtGui/QIcon';
/**

> The QMenuBar class provides a menu widget for use in menu bars, context menus, and other popup menus.

* **This class is a JS wrapper around Qt's [QMenuBar class](https://doc.qt.io/qt-5/qmenu.html)**

### Example

```javascript
const { QMenuBar, QMainWindow } = require("@nodegui/nodegui");
const win = new QMainWindow();
const menu = new QMenuBar();
const menuBar = new QMenuBar();
win.setMenuBar(menuBar);
win.show();
global.win = win;
```
 */
export declare class QMenuBar extends QWidget<QMenuBarSignals> {
    constructor(arg?: QWidget<QWidgetSignals> | NativeElement);
    addSeparator(): QAction;
    actionAt(pt: QPoint): QAction;
    actionGeometry(act: QAction): QRect;
    activeAction(): QAction;
    addMenu(menuOrStringOrIcon: QMenu | QIcon | string, title?: string): QMenu;
    clear(): void;
    cornerWidget(corner?: Corner): QWidget;
    insertMenu(before: QAction, menu: QMenu): QAction;
    insertSeparator(before: QAction): QAction;
    setActiveAction(act: QAction): void;
    setCornerWidget(widget: QWidget, corner?: Corner): void;
    isNativeMenuBar(): boolean;
    setNativeMenuBar(nativeMenuBar: boolean): void;
    isDefaultUp(): boolean;
    setDefaultUp(isDefaultUp: boolean): void;
}
export interface QMenuBarSignals extends QWidgetSignals {
    hovered: (action: NativeElement) => void;
    triggered: (action: NativeElement) => void;
}

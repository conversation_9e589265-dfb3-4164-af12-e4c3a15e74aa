"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QStackedLayout = void 0;
const addon_1 = __importDefault(require("../utils/addon"));
const QLayout_1 = require("./QLayout");
const WrapperCache_1 = require("../core/WrapperCache");
const helpers_1 = require("../utils/helpers");
/**

> The QStackedLayout class provides a stack of widgets where only one widget is visible at a time

* **This class is a JS wrapper around Qt's [QStackedLayout](https://doc.qt.io/qt-5/qstackedlayout.html)**

### Example

```javascript
const { QStackedLayout, QWidget, <PERSON><PERSON>abel, QBoxLayout, QCombobox } = require("@nodegui/nodegui");

const centralWidget = new QWidget();
centralWidget.setObjectName('myroot');
const rootLayout = new QBoxLayout(Direction.TopToBottom);
centralWidget.setLayout(rootLayout);

const stackedLayout = new QStackedLayout()

const page1 = new QWidget();
const page1_layout = new FlexLayout();
page1.setLayout(page1_layout)
const label1 = new QLabel();
label1.setText("This is page 1")
page1_layout.addWidget(label1);

const page2 = new QWidget();
const page2_layout = new FlexLayout();
page2.setLayout(page2_layout)
const label2 = new QLabel();
label2.setText("This is page 2")
page2_layout.addWidget(label2);

const page3 = new QWidget();
const page3_layout = new FlexLayout();
page3.setLayout(page3_layout)
const label3 = new QLabel();
label3.setText("This is page 3")
page3_layout.addWidget(label3);

stackedLayout.addWidget(page1)
stackedLayout.addWidget(page2)
stackedLayout.addWidget(page3)

const combobox = new QComboBox()
combobox.addItems(["Page 1", "Page 2", "Page 3"])

combobox.addEventListener("currentIndexChanged", (index) => stackedLayout.setCurrentIndex(index));

rootLayout.addWidget(combobox);

const currentIndexLabel = new QLabel()
currentIndexLabel.setText(`Current Index: ${stackedLayout.currentIndex()}`)

stackedLayout.addEventListener("currentChanged", (index) => {
  currentIndexLabel.setText(`Current Index: ${index}`)
});

rootLayout.addWidget(currentIndexLabel);

rootLayout.addLayout(stackedLayout);

*/
class QStackedLayout extends QLayout_1.QLayout {
    constructor(arg) {
        let native;
        if ((0, helpers_1.checkIfNativeElement)(arg)) {
            native = arg;
        }
        else if (arg != null) {
            const parent = arg;
            native = new addon_1.default.QStackedLayout(parent.native);
        }
        else {
            native = new addon_1.default.QStackedLayout();
        }
        super(native);
    }
    addWidget(widget) {
        this.native.addWidget(widget.native);
    }
    removeWidget(widget) {
        this.native.removeWidget(widget.native);
    }
    setCurrentIndex(index) {
        this.native.setCurrentIndex(index);
    }
    insertWidget(index, widget) {
        this.native.insertWidget(index, widget.native);
    }
    setCurrentWidget(widget) {
        this.native.setCurrentWidget(widget.native);
    }
    currentIndex() {
        return this.native.currentIndex();
    }
    currentWidget() {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.currentWidget());
    }
    widget(index) {
        return WrapperCache_1.wrapperCache.getWrapper(this.native.widget(index));
    }
    count() {
        return this.native.count();
    }
    indexOf(widget) {
        return this.native.indexOf(widget.native);
    }
    setStackingMode(stackingMode) {
        this.native.setStackingMode(stackingMode);
    }
    stackingMode() {
        return this.native.stackingMode();
    }
}
exports.QStackedLayout = QStackedLayout;
WrapperCache_1.wrapperCache.registerWrapper('QStackedLayoutWrap', QStackedLayout);
